<template>
  <div class="attendance-page art-full-height art-page-view">
    <!-- 提示信息 -->
    <ElAlert type="info" :closable="false" class="mb-20">
      考勤配置请在对应的生命周期项目中进行创建和编辑，此页面用于查看配置和打卡记录。
    </ElAlert>

    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @reset="handleReset"
      @search="handleSearch"
      :showExpand="true"
    />

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll" />

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="tableConfig"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 打卡地点列插槽 -->
        <template #location="{ row }">
          <ElTooltip :content="row.location" placement="top">
            <span class="location-text">{{ row.location }}</span>
          </ElTooltip>
        </template>

        <!-- 打卡范围列插槽 -->
        <template #location_range="{ row }">
          <span>{{ row.location_range }}米</span>
        </template>

        <!-- 打卡时间列插槽 -->
        <template #checkin_time="{ row }">
          <span>{{ timestampToTime(row.checkin_time) || '-' }}</span>
        </template>

        <!-- 参与人员列插槽 -->
        <template #users="{ row }">
          <div v-if="row.users && row.users.length > 0">
            <ElPopover placement="right" width="400" trigger="hover">
              <template #reference>
                <ElButton type="primary" link>
                  <ElIcon><User /></ElIcon>
                  {{ row.users.length }}人
                </ElButton>
              </template>
              <template #default>
                <div>
                  <h4
                    style="margin: 0 0 10px; font-size: 14px; color: var(--el-text-color-primary)"
                  >
                    参与人员
                  </h4>
                  <div class="participants-list">
                    <div v-for="user in row.users" :key="user.id" class="participant-item">
                      <ElAvatar
                        :src="user.avatar"
                        :size="24"
                        :icon="UserFilled"
                        class="participant-avatar"
                      />
                      <span class="participant-name">{{ user.nickname || user.name }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </ElPopover>
          </div>
          <span v-else>-</span>
        </template>

        <!-- 操作列插槽 -->
        <template #operation="{ row }">
          <div style="display: flex; flex-wrap: wrap; gap: 5px">
            <ArtButtonTable type="primary" @click="showRecordDialog(row)">
              打卡记录
            </ArtButtonTable>
          </div>
        </template>
      </ArtTable>

      <!-- 打卡记录对话框 -->
      <CheckinRecordDialog
        v-model="recordDialogVisible"
        :config="currentConfig"
        @success="handleRecordSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'Attendance' })

  // Vue 核心
  import { ref, computed } from 'vue'

  // UI 框架
  import { ElAlert, ElPopover, ElTooltip, ElAvatar } from 'element-plus'
  import { User, UserFilled } from '@element-plus/icons-vue'

  // 工具函数和 composables
  import { useTable } from '@/composables/useTable'
  import { formatDate } from '@/utils/dataprocess/format'

  // 组件
  import CheckinRecordDialog from './components/CheckinRecordDialog.vue'
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'

  // API
  import { getCheckinConfigs, timestampToTime } from '@/api/admin/attendance'

  // 类型定义
  import type { CheckinConfig } from '@/types/api/attendance'
  import type { SearchFormItem } from '@/types'

  // 表格配置
  const tableConfig = {
    rowKey: 'id',
    height: undefined,
    maxHeight: '600px'
  }

  // 对话框
  const recordDialogVisible = ref(false)
  const currentConfig = ref<CheckinConfig | null>(null)

  // 搜索表单状态
  const searchFormState = ref({})

  // 搜索表单配置
  const searchFormItems = computed<SearchFormItem[]>(() => [])

  // 使用 useTable composable
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    onPageSizeChange,
    onCurrentPageChange,
    searchData,
    resetSearch
  } = useTable<CheckinConfig>({
    core: {
      apiFn: async (params: any) => {
        const response = await getCheckinConfigs({
          page: params.current,
          per_page: params.size
        })

        return {
          records: response.data || [],
          total: response.meta?.total || 0,
          current: params.current,
          size: params.size
        }
      },
      apiParams: {
        current: 1,
        size: 10
      },
      columnsFactory: () => [
        { prop: 'id', label: 'ID', width: 100 },
        {
          prop: 'location',
          label: '打卡地点',
          minWidth: 250,
          useSlot: true,
          slotName: 'location'
        },
        {
          prop: 'location_range',
          label: '打卡范围',
          width: 100,
          useSlot: true,
          slotName: 'location_range'
        },
        {
          prop: 'checkin_time',
          label: '打卡时间',
          width: 120,
          useSlot: true,
          slotName: 'checkin_time'
        },
        {
          prop: 'users',
          label: '参与人员',
          width: 100,
          useSlot: true,
          slotName: 'users'
        },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 150,
          formatter: (row: CheckinConfig) =>
            row.created_at ? formatDate(row.created_at * 1000, 'YYYY-MM-DD HH:mm') : '-'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          fixed: 'right',
          useSlot: true
        }
      ],
      immediate: true
    }
  })

  // 处理搜索
  const handleSearch = () => {
    searchData()
  }

  // 处理重置
  const handleReset = () => {
    searchFormState.value = {}
    resetSearch()
  }

  // 显示打卡记录对话框
  const showRecordDialog = (row: CheckinConfig) => {
    currentConfig.value = row
    recordDialogVisible.value = true
  }

  // 记录对话框成功
  const handleRecordSuccess = () => {
    // 打卡记录不影响配置列表，无需刷新
  }
</script>

<style lang="scss" scoped>
  .attendance-page {
    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  .location-text {
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .participants-list {
    max-height: 200px;
    overflow-y: auto;

    .participant-item {
      display: flex;
      align-items: center;
      padding: 4px 0;

      .participant-avatar {
        margin-right: 8px;
      }

      .participant-name {
        font-size: 14px;
        color: var(--el-text-color-primary);
      }
    }
  }
</style>
