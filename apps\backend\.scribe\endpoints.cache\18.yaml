## Autogenerated by <PERSON>ri<PERSON>. DO NOT MODIFY.

name: 标签管理
description: |-

  管理系统标签库
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/tags
    metadata:
      groupName: 标签管理
      groupDescription: |-

        管理系统标签库
      subgroup: ''
      subgroupDescription: ''
      title: 获取标签列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页数量.
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      name:
        name: name
        description: 标签名称搜索.
        required: false
        example: 重要
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      category:
        name: category
        description: 标签分类筛选.
        required: false
        example: 重要
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 20
      name: 重要
      category: 重要
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":68,"name":"\u5f85\u5de1\u67e5\u4fdd\u517b","category":"\u72b6\u6001"},{"id":69,"name":"\u7d27\u6025\u7ef4\u4fee","category":"\u72b6\u6001"}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/tags/all
    metadata:
      groupName: 标签管理
      groupDescription: |-

        管理系统标签库
      subgroup: ''
      subgroupDescription: ''
      title: 获取所有标签（用于选择器）
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":8,"name":"备用待命","category":"状态"},{"id":19,"name":"安全检查","category":"操作"},{"id":5,"name":"已到场确认","category":"状态"},{"id":4,"name":"已维修","category":"状态"},{"id":17,"name":"常规保养","category":"优先级"},{"id":18,"name":"年度质控","category":"优先级"},{"id":1,"name":"待巡查保养","category":"状态"},{"id":2,"name":"待维修","category":"状态"},{"id":20,"name":"性能测试","category":"操作"},{"id":7,"name":"故障停机","category":"状态"},{"id":14,"name":"校准检测","category":"任务"},{"id":6,"name":"正常运行","category":"状态"},{"id":16,"name":"紧急维修","category":"优先级"},{"id":3,"name":"维修中","category":"状态"},{"id":15,"name":"计量检定","category":"任务"},{"id":9,"name":"设备巡检","category":"任务"},{"id":12,"name":"设备盘点","category":"任务"},{"id":10,"name":"设备维保","category":"任务"},{"id":11,"name":"设备质控","category":"任务"},{"id":13,"name":"预防性维护","category":"任务"}]'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/tags
    metadata:
      groupName: 标签管理
      groupDescription: |-

        管理系统标签库
      subgroup: ''
      subgroupDescription: ''
      title: 创建标签
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 标签名称.
        required: true
        example: 重要
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      category:
        name: category
        description: 标签分类.
        required: false
        example: 重要
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 重要
      category: 重要
    fileParameters: []
    responses:
      -
        status: 201
        content: '{"id":70,"name":"重要","category":"重要"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/tags/{id}'
    metadata:
      groupName: 标签管理
      groupDescription: |-

        管理系统标签库
      subgroup: ''
      subgroupDescription: ''
      title: 更新标签
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 标签ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 标签名称.
        required: false
        example: 重要
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      category:
        name: category
        description: 标签分类.
        required: false
        example: 重要
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 重要
      category: 重要
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"name":"重要","category":"重要"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/tags/{id}'
    metadata:
      groupName: 标签管理
      groupDescription: |-

        管理系统标签库
      subgroup: ''
      subgroupDescription: ''
      title: 删除标签
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 标签ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 204
        content: ''
        headers:
          cache-control: 'no-cache, private'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
