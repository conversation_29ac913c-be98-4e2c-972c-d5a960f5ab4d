name: 二维码生成
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/admin/qrcode/generate
    metadata:
      groupName: 二维码生成
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 生成二维码
      description: ''
      authenticated: false
      deprecated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      content:
        name: content
        description: 要生成二维码的内容
        required: false
        example: '1222'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      size:
        name: size
        description: 二维码大小，默认300，范围50-1000
        required: false
        example: 300
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      color:
        name: color
        description: 二维码颜色，十六进制颜色码
        required: false
        example: '#ff0000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      background_color:
        name: background_color
        description: 背景颜色，十六进制颜色码
        required: false
        example: '#ffffff'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      margin:
        name: margin
        description: 二维码边距，默认1，范围0-50
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      content: '1222'
      size: 300
      color: '#ff0000'
      background_color: '#ffffff'
      margin: 1
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "qrcode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
