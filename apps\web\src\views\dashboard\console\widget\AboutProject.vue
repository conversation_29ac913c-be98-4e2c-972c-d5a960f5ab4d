<template>
  <div class="card quick-entrance art-custom-card">
    <div class="content">
      <h2 class="box-title">快速入口</h2>
      <p class="description">提供常用功能的快捷访问，提升工作效率</p>

      <div class="entrance-grid">
        <el-button
          v-for="item in quickEntrances"
          :key="item.key"
          class="entrance-btn"
          :style="{ '--btn-color': item.color }"
          @click="handleQuickAction(item)"
        >
          <div class="btn-content">
            <i class="iconfont-sys btn-icon" v-html="item.icon"></i>
            <span class="btn-text">{{ item.title }}</span>
          </div>
        </el-button>
      </div>
    </div>
    <img class="right-img" src="@imgs/draw/draw1.png" alt="quick-entrance" />
  </div>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'

  // 快速入口配置
  interface QuickEntrance {
    key: string
    title: string
    icon: string
    color: string
    action: string
  }

  const quickEntrances = ref<QuickEntrance[]>([
    {
      key: 'add-asset',
      title: '快速添加资产',
      icon: '&#xe7c5;',
      color: '#67C23A',
      action: '跳转到资产添加页面'
    },
    {
      key: 'alert-center',
      title: '查看告警中心',
      icon: '&#xe7aa;',
      color: '#F56C6C',
      action: '跳转到告警中心页面'
    },
    {
      key: 'data-export',
      title: '数据导出',
      icon: '&#xe7b2;',
      color: '#409EFF',
      action: '打开数据导出功能'
    },
    {
      key: 'user-management',
      title: '用户管理',
      icon: '&#xe721;',
      color: '#9C27B0',
      action: '跳转到用户管理页面'
    },
    {
      key: 'system-config',
      title: '系统配置',
      icon: '&#xe7b9;',
      color: '#909399',
      action: '跳转到系统配置页面'
    },
    {
      key: 'api-docs',
      title: 'API文档',
      icon: '&#xe7c7;',
      color: '#FF9800',
      action: '打开API文档页面'
    }
  ])

  // 处理快速入口点击事件
  const handleQuickAction = (item: QuickEntrance) => {
    ElMessage.success(`${item.action}（功能开发中）`)

    // TODO: 后续可以根据需要实现真实的路由跳转
    // 例如：
    // switch (item.key) {
    //   case 'add-asset':
    //     router.push('/asset/add')
    //     break
    //   case 'alert-center':
    //     router.push('/alert/center')
    //     break
    //   // ... 其他路由
    // }
  }
</script>

<style lang="scss" scoped>
  .quick-entrance {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    height: 300px;
    padding: 20px;

    .content {
      flex: 1;
    }

    h2 {
      margin-top: 10px;
      font-size: 20px;
      font-weight: 500;
      color: var(--art-gray-900) !important;
    }

    .description {
      margin-top: 5px;
      font-size: 14px;
      color: var(--art-gray-600);
    }
  }

  .entrance-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    width: 600px;
    margin-top: 35px;
  }

  .entrance-btn {
    width: 180px;
    height: 60px;
    padding: 0;
    background: var(--art-bg-color);
    border: none;
    border-radius: calc(var(--custom-radius) / 2 + 2px) !important;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--btn-color);
      box-shadow: 0 8px 16px rgb(0 0 0 / 15%);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
    }

    .btn-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .btn-icon {
      font-size: 20px;
      color: var(--btn-color);
      transition: all 0.3s ease;
    }

    .btn-text {
      font-size: 12px;
      font-weight: 500;
      color: var(--art-gray-800);
      white-space: nowrap;
    }

    &:hover .btn-icon {
      transform: scale(1.1);
    }

    &:hover .btn-text {
      color: var(--btn-color);
    }
  }

  .right-img {
    width: 350px;
    height: 260px;
    object-fit: contain;
  }

  // 响应式设计 - iPad Pro及以下
  @media screen and (max-width: $device-ipad-pro) {
    .quick-entrance {
      height: auto;
    }

    .entrance-grid {
      gap: 12px;
      width: 470px;
      margin-top: 20px;
    }

    .entrance-btn {
      width: 150px;
      height: 55px;

      .btn-icon {
        font-size: 18px;
      }

      .btn-text {
        font-size: 11px;
      }
    }

    .right-img {
      width: 300px;
      height: 230px;
    }
  }

  // 响应式设计 - iPad垂直及以下
  @media screen and (max-width: $device-ipad-vertical) {
    .entrance-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      width: 100%;
    }

    .entrance-btn {
      width: 100%;
      max-width: 190px;
    }

    .right-img {
      display: none;
    }
  }

  // 响应式设计 - 手机端
  @media screen and (max-width: $device-phone) {
    .quick-entrance {
      padding: 0 15px;
    }

    .entrance-grid {
      grid-template-columns: 1fr;
      gap: 10px;
      margin-top: 15px;
    }

    .entrance-btn {
      width: 100%;
      max-width: none;
      height: 50px;

      .btn-content {
        flex-direction: row;
        gap: 12px;
      }

      .btn-icon {
        font-size: 16px;
      }

      .btn-text {
        font-size: 13px;
      }
    }
  }
</style>
