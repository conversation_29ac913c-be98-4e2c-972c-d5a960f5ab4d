<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ConfigsRequest;
use App\Http\Resources\Admin\ConfigResource;
use App\Services\ConfigService;

/**
 * @group 配置管理
 *
 * 系统配置管理接口
 */
class ConfigController extends Controller
{
    public function __construct(
        private ConfigService $configService
    ) {}

    /**
     * 获取所有配置
     *
     * 获取系统配置和上传配置
     *
     * @response {
     *      "system": {"system_name": "设备云管理系统", "system_logo": "https://example.com/logo.png"},
     *      "upload": {"storage_type": "alioss|qiniu|local", "aliyun_access_key": "1234567890", "aliyun_access_secret": "1234567890", "aliyun_bucket": "tc-kyx", "aliyun_region": "oss-cn-hangzhou", "aliyun_endpoint": "oss-cn-hangzhou.aliyuncs.com"}
     * }
     */
    public function index()
    {
        $configs = $this->configService->getAllConfigs();

        return new ConfigResource($configs);
    }

    /**
     * 更新配置
     *
     * 更新系统配置和上传配置
     *
     * @bodyParam configs 配置数据 Example: {"system": {"system_name": "设备云管理系统", "system_logo": "https://example.com/logo.png"}, "upload": {"storage_type": "alioss|qiniu|local", "aliyun_access_key": "1234567890", "aliyun_access_secret": "1234567890", "aliyun_bucket": "tc-kyx", "aliyun_region": "oss-cn-hangzhou", "aliyun_endpoint": "oss-cn-hangzhou.aliyuncs.com"}}
     * @bodyParam configs.system.system_name string 系统网站名称 Example: 设备云管理系统
     * @bodyParam configs.system.system_logo string 系统网站Logo Example: https://example.com/logo.png
     * @bodyParam configs.upload.storage_type string 上传文件存储类型 Example: local|alioss|qiniu
     * @bodyParam configs.upload.aliyun_access_key string 阿里云AccessKey Example: 1234567890
     * @bodyParam configs.upload.aliyun_access_secret string 阿里云AccessSecret Example: 1234567890
     * @bodyParam configs.upload.aliyun_bucket string 阿里云Bucket Example: tc-kyx
     * @bodyParam configs.upload.aliyun_region string 阿里云区域 Example: oss-cn-hangzhou
     * @bodyParam configs.upload.aliyun_endpoint string 阿里云Endpoint Example: oss-cn-hangzhou.aliyuncs.com
     *
     * @return \Illuminate\Http\JsonResponse 配置更新成功 {"message": "配置更新成功"}
     */
    public function update(ConfigsRequest $request)
    {
        $data = $request->validated();

        $this->configService->update($data['configs']);

        return response()->json([
            'message' => '配置更新成功',
        ]);
    }

    /**
     * 清除配置缓存
     *
     * 清除所有配置缓存
     */
    public function clearCache()
    {
        $this->configService->clearCache();

        return response()->json([
            'message' => '配置缓存清除成功',
        ]);
    }
}
