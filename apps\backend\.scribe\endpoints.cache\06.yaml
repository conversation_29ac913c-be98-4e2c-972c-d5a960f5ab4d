## Autogenerated by Scribe. DO NOT MODIFY.

name: 角色管理
description: |-

  管理系统角色
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/roles
    metadata:
      groupName: 角色管理
      groupDescription: |-

        管理系统角色
      subgroup: ''
      subgroupDescription: ''
      title: 获取角色列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      search:
        name: search
        description: 搜索关键词（角色名称）.
        required: false
        example: admin
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数.
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      search: admin
      page: 1
      per_page: 20
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":1,"name":"admin","guard_name":null,"description":"\u7cfb\u7edf\u7ba1\u7406\u5458","menus":[{"menu_id":1,"permission_ids":[]},{"menu_id":2,"permission_ids":[]},{"menu_id":3,"permission_ids":[]},{"menu_id":4,"permission_ids":[1,2,3,4,5]},{"menu_id":5,"permission_ids":[9,6,7,8]},{"menu_id":6,"permission_ids":[]},{"menu_id":7,"permission_ids":[10,11,12]},{"menu_id":8,"permission_ids":[13,14]},{"menu_id":9,"permission_ids":[]},{"menu_id":10,"permission_ids":[]},{"menu_id":11,"permission_ids":[15,16,17]},{"menu_id":12,"permission_ids":[18,19,20]},{"menu_id":13,"permission_ids":[21,22,23]},{"menu_id":14,"permission_ids":[24,25,26]},{"menu_id":15,"permission_ids":[30,27,28,29]},{"menu_id":16,"permission_ids":[31,32]},{"menu_id":17,"permission_ids":[33,34,35]},{"menu_id":18,"permission_ids":[36,37,38,39]}],"created_at":"2025-08-22T09:22:05.000000Z","updated_at":"2025-08-22T09:22:05.000000Z"},{"id":1,"name":"admin","guard_name":null,"description":"\u7cfb\u7edf\u7ba1\u7406\u5458","menus":[{"menu_id":1,"permission_ids":[]},{"menu_id":2,"permission_ids":[]},{"menu_id":3,"permission_ids":[]},{"menu_id":4,"permission_ids":[1,2,3,4,5]},{"menu_id":5,"permission_ids":[9,6,7,8]},{"menu_id":6,"permission_ids":[]},{"menu_id":7,"permission_ids":[10,11,12]},{"menu_id":8,"permission_ids":[13,14]},{"menu_id":9,"permission_ids":[]},{"menu_id":10,"permission_ids":[]},{"menu_id":11,"permission_ids":[15,16,17]},{"menu_id":12,"permission_ids":[18,19,20]},{"menu_id":13,"permission_ids":[21,22,23]},{"menu_id":14,"permission_ids":[24,25,26]},{"menu_id":15,"permission_ids":[30,27,28,29]},{"menu_id":16,"permission_ids":[31,32]},{"menu_id":17,"permission_ids":[33,34,35]},{"menu_id":18,"permission_ids":[36,37,38,39]}],"created_at":"2025-08-22T09:22:05.000000Z","updated_at":"2025-08-22T09:22:05.000000Z"}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/roles
    metadata:
      groupName: 角色管理
      groupDescription: |-

        管理系统角色
      subgroup: ''
      subgroupDescription: ''
      title: 创建角色
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 角色名称.
        required: true
        example: 管理员
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 角色描述.
        required: false
        example: 系统管理员角色
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 管理员
      description: 系统管理员角色
    fileParameters: []
    responses:
      -
        status: 201
        content: '{"id":18,"name":"管理员","guard_name":null,"description":"系统管理员角色","created_at":"2025-08-25T06:08:32.000000Z","updated_at":"2025-08-25T06:08:32.000000Z"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/roles/{id}'
    metadata:
      groupName: 角色管理
      groupDescription: |-

        管理系统角色
      subgroup: ''
      subgroupDescription: ''
      title: 获取角色详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the role.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 角色ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      role: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"name":"admin","guard_name":null,"description":"系统管理员","menus":[{"menu_id":1,"permission_ids":[]},{"menu_id":2,"permission_ids":[]},{"menu_id":3,"permission_ids":[]},{"menu_id":4,"permission_ids":[1,2,3,4,5]},{"menu_id":5,"permission_ids":[9,6,7,8]},{"menu_id":6,"permission_ids":[]},{"menu_id":7,"permission_ids":[10,11,12]},{"menu_id":8,"permission_ids":[13,14]},{"menu_id":9,"permission_ids":[]},{"menu_id":10,"permission_ids":[]},{"menu_id":11,"permission_ids":[15,16,17]},{"menu_id":12,"permission_ids":[18,19,20]},{"menu_id":13,"permission_ids":[21,22,23]},{"menu_id":14,"permission_ids":[24,25,26]},{"menu_id":15,"permission_ids":[30,27,28,29]},{"menu_id":16,"permission_ids":[31,32]},{"menu_id":17,"permission_ids":[33,34,35]},{"menu_id":18,"permission_ids":[36,37,38,39]}],"created_at":"2025-08-22T09:22:05.000000Z","updated_at":"2025-08-22T09:22:05.000000Z"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/admin/roles/{id}'
    metadata:
      groupName: 角色管理
      groupDescription: |-

        管理系统角色
      subgroup: ''
      subgroupDescription: ''
      title: 更新角色
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the role.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 角色ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      role: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 角色名称.
        required: true
        example: 管理员
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 角色描述.
        required: false
        example: 系统管理员角色
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 管理员
      description: 系统管理员角色
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"name":"管理员","guard_name":null,"description":"系统管理员角色","menus":[{"menu_id":1,"permission_ids":[]},{"menu_id":2,"permission_ids":[]},{"menu_id":3,"permission_ids":[]},{"menu_id":4,"permission_ids":[1,2,3,4,5]},{"menu_id":5,"permission_ids":[9,6,7,8]},{"menu_id":6,"permission_ids":[]},{"menu_id":7,"permission_ids":[10,11,12]},{"menu_id":8,"permission_ids":[13,14]},{"menu_id":9,"permission_ids":[]},{"menu_id":10,"permission_ids":[]},{"menu_id":11,"permission_ids":[15,16,17]},{"menu_id":12,"permission_ids":[18,19,20]},{"menu_id":13,"permission_ids":[21,22,23]},{"menu_id":14,"permission_ids":[24,25,26]},{"menu_id":15,"permission_ids":[30,27,28,29]},{"menu_id":16,"permission_ids":[31,32]},{"menu_id":17,"permission_ids":[33,34,35]},{"menu_id":18,"permission_ids":[36,37,38,39]}],"created_at":"2025-08-22T09:22:05.000000Z","updated_at":"2025-08-25T06:08:32.000000Z"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/roles/{id}'
    metadata:
      groupName: 角色管理
      groupDescription: |-

        管理系统角色
      subgroup: ''
      subgroupDescription: ''
      title: 删除角色
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the role.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 角色ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      role: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 204
        content: ''
        headers:
          cache-control: 'no-cache, private'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/roles/{role_id}/menu-permissions/assign'
    metadata:
      groupName: 角色管理
      groupDescription: |-

        管理系统角色
      subgroup: ''
      subgroupDescription: ''
      title: 分配菜单权限
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      role_id:
        name: role_id
        description: 'The ID of the role.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: 角色ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      role_id: 1
      role: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      permissions:
        name: permissions
        description: 权限数组.
        required: true
        example:
          -
            menu_id: 1
            permission_ids:
              - 1
              - 2
              - 3
              - 4
              - 5
          -
            menu_id: 1
            permission_ids:
              - 1
              - 2
              - 3
              - 4
              - 5
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions[].menu_id':
        name: 'permissions[].menu_id'
        description: 'The <code>id</code> of an existing record in the menus table.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].permission_ids':
        name: 'permissions[].permission_ids'
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions.*.menu_id':
        name: 'permissions.*.menu_id'
        description: 菜单ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.permission_ids':
        name: 'permissions.*.permission_ids'
        description: 菜单权限ID数组.
        required: true
        example:
          - 1
          - 2
          - 3
          - 4
          - 5
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.permission_ids.*':
        name: 'permissions.*.permission_ids.*'
        description: 'nullable 菜单权限ID，为空表示只有菜单访问权限.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      permissions:
        -
          menu_id: 1
          permission_ids:
            - 1
            - 2
            - 3
            - 4
            - 5
        -
          menu_id: 1
          permission_ids:
            - 1
            - 2
            - 3
            - 4
            - 5
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "message": "权限分配成功"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
