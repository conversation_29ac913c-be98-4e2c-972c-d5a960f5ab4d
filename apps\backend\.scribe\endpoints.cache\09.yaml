## Autogenerated by <PERSON>ri<PERSON>. DO NOT MODIFY.

name: 附件管理
description: 附件的上传、下载、删除等操作
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/attachments
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取附件列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页数量
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      file_name:
        name: file_name
        description: 文件名（模糊搜索）
        required: false
        example: example.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      start_time:
        name: start_time
        description: 开始时间
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_time:
        name: end_time
        description: 结束时间
        required: false
        example: '2024-12-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 20
      file_name: example.pdf
      start_time: '2024-01-01'
      end_time: '2024-12-31'
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":"1","file_name":"u=*********,4182719556&fm=253&app=138&f=JPEG.jpg","file_path":"attachments\/2025\/08\/23\/wizRN7suzBiqOkstFnCDKNVJnJAomhxK.jpg","file_size":59351,"mime_type":"image\/jpeg","storage_type":"local","md5_hash":"ed1dfdad2ee8a19c0ee4de2d4f2b5038","file_url":"http:\/\/localhost:8005\/storage\/attachments\/2025\/08\/23\/wizRN7suzBiqOkstFnCDKNVJnJAomhxK.jpg","formatted_file_size":"57.96 KB","created_at":1755955822,"updated_at":1755955822,"relation_id":"9","attachable_type":"App\\Models\\CheckinRecord","attachable_id":"16","category":"general","description":null},{"id":"1","file_name":"u=*********,4182719556&fm=253&app=138&f=JPEG.jpg","file_path":"attachments\/2025\/08\/23\/wizRN7suzBiqOkstFnCDKNVJnJAomhxK.jpg","file_size":59351,"mime_type":"image\/jpeg","storage_type":"local","md5_hash":"ed1dfdad2ee8a19c0ee4de2d4f2b5038","file_url":"http:\/\/localhost:8005\/storage\/attachments\/2025\/08\/23\/wizRN7suzBiqOkstFnCDKNVJnJAomhxK.jpg","formatted_file_size":"57.96 KB","created_at":1755955822,"updated_at":1755955822,"relation_id":"9","attachable_type":"App\\Models\\CheckinRecord","attachable_id":"16","category":"general","description":null}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/upload
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 上传附件（本地上传）
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      file:
        name: file
        description: 要上传的文件（最大10MB）
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters: []
    fileParameters:
      file: null
    responses:
      -
        status: 201
        content: '{"id":"17","file_name":"test.jpg","file_path":"attachments/2025/08/25/XFO6glNzevSr9FQEQOyTIYgHhwUVpurf.jpg","file_size":10240,"mime_type":"image/jpeg","storage_type":"local","md5_hash":"d41d8cd98f00b204e9800998ecf8427e","file_url":"http://localhost:8005/storage/attachments/2025/08/25/XFO6glNzevSr9FQEQOyTIYgHhwUVpurf.jpg","formatted_file_size":"10.00 KB","created_at":1756102112,"updated_at":1756102112,"relation_id":null,"attachable_type":null,"attachable_id":null,"category":null,"description":null}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/attachments/{id}'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取附件详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 附件ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 404
        content: '{"message":"No query results for model [App\\Models\\Attachment] 16"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/attachments/{attachment}/download'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 下载附件
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      attachment:
        name: attachment
        description: 'The attachment.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 附件ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      attachment: 1
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: !!binary /9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAMgAyADASIAAhEBAxEB/8QAHAAAAgMBAQEBAAAAAAAAAAAAAgMBBAUABgcI/8QAQxAAAgICAQMCBAQEBAUDAwIHAQIAAwQRIQUSMUFRBhMiYRQycYEjQlKRFaGx0QczYnLBJEOCFlPwNESS4SU1Y6Lx/8QAGwEAAgMBAQEAAAAAAAAAAAAAAQIAAwQFBgf/xAAwEQACAwACAgICAgEEAQMFAAAAAQIDEQQSITEFEyJBMlEUFSNhcYEGM1IWQqGxwf/aAAwDAQACEQMRAD8A+vzp0kTkm4GCfEMiRqTCIDU7XMKdJ6GOEgyZ0ZAIAhSJIjoDCB4nTgJMsQCRCgiTIQKROk6h9ABnahanahAQo1OhanakTIQZwk6k6hAyJInSdSEOnQgu4QWDAC9bk9sZ2ztQBQvtk61D1O1AADUn0hanaitEFztRnbI1AFC5Ih6gkQBInGTI3IEEiRqFOiNBAI4g6hmAWihRB1B1CJg7iMYgiA3mEW4iyZW2MdIM7c7cRhOk+sjfMIekAGMHiFBWMAgFbwkCGBIAjFEKEZAEMCSFhAR0hNB1O1D1O1D5BosjcjXEZ2yCJMG0SViyseRFmBobRDCCRGsIsxB0xTQdQyNyIrDoJEjUORqKTQG3A5jiOIBWBh0DUjUZqRqKHQCsjRjCJGpA6BOhEQZAkGD6w5BEdABE71k6kahDoJ8QYcjW4CAzpJEiQhIgnzC8SDAMQSNQZJHEGKyIhjxBEkyN6gHOMGTvciQJ25HrIkbisKQRIiz5kk7kQESOnbnSPMA6O3O3OPEiME9LJEIiDOthzToMKRIQEzpxnSE06dOkiMgkSROnCMiBidIEIR0KzgIUgeYQh0BwkzpMKIdOnTo2AOkyJIEOEO1O1C1xJAgA0QBCAkgSdQgO1CE4CEBxIAGdqFqTqDCaDqdqHqdqDCaBqdqHqRqKwaARO1D1BI5i4FMEjiAwjSIBEUbRZgwyIJgYyIJgkyZDHiI2EFiYEIkagGK2MjiYBMknmAfMRjkEyDOnStkI5nSZGuYCHCGJAEYBBgA0EYBBQRqiRIVslVjVWcqxoWWKJTJghZPbDAk6liiJoGp2oepOoejBovUFhG6gkSdQ6IYRREsERZEVxHixDCKIlhhFMBEcSxMTqCY0iQViOJYKnahgSdQdCAa4gkRup3bsQdSCtSNRvbxA1B0CDqRqGRI1F6hAIgkQyILCDAg6kESSJwkJgEiGYMhAZG4UExSEGQJMiFBOMicZGoRiD4gGGfEEiIyIEwTCkGQcHmQZJ8wYBjpB8zjIgYSDIhHxAijJEzoJ8zgeZAhGcIJMkGRkPUSDCkTss5oMiTIMUBBEiTudB6CROkToyCFOEiSIUQIQwIIhiOmA7UISJIjewEzgJ0kRkBnanakzo2gI1CUTtThBpAtSQJEIRgHQh5kSR5kAwhC1IEJmVV2xAA9TI2l5YjOAk6lavqGLbeakuQuPTctbixnGf8WBpr2dO1OnRgEa1I4PrMrr2YaMdakYh7D5HtPP0Zt+Nb312tv1BOwZyOV8pXRcq2aa+NKyPZHttQSInAyhmYqW60SOR7GWdToQmpxUkUNNPGLIgkRpEAiRkTEkQGEaRAYRWWJioDeIwxbeJUxwDBMIyDFY6AMEiHBPiKODqdqSBJ1BhAdSVXciyyupO+x1RfdjoSarEtQPW6uh8Mp2DBhM/YYWGqyVEaqwYI2Qix6rIQRyiWRgUyZKrD1OELUujEpbOAk9skeJ0tUQEanakxN+TTjKGucICdAmSWRWsiGakanI62oHRgynwRJ1BmrQgFeIplj4JEDiMmVWWKKy0wiyIriWJlciR2xzDQJnnsv4kQZDY/TsS3OtU6Y18Kp9iYa+POx/iM7EjZKztTEwfiP5uauHnYtmFe/5A/hv0M3u0iS3jzq/kgxsTA1OAh6kgSnqN2F9sErHakEQOIdElYPbGkQSOJW4jJiSNQCNxpU7kdsRxGFdsjUaYs+YrQUARzIIknzIiEBMEw9SCICAbkSSOZ0hCJGpM6EYgwGktBJikSBkGTIkY4JkQjAMRsdEGDucTOi6MduDCgmRBBM7c4+ZEhCdyN8zpBgCesnHxI3OLTsnLIgnzJJ1IMAURuQTxO8wSYAnbkgwZ29GDQ4HCUwN7hD0jJkwZ5MIRYMMR0wBiTBEncZMDCEmRJjJismdIhQtgOkiRJEKYGEIQgesXflVY1ZZz+0k5xgu0niIk34LEkDmVcbNpyv+Wf7y0JK7I2LYPQSi0/IQnn+v5Fq2LSCyoRv9Z6ASn1LAXOx+0aDrypmbn1zsolGHsNMoxmnI8gCVcMpII5BHpPa9Pv8AxOHXYDzrR/WeNtqsptNVikODrXvPTfD1VtWM/wAwEAnYBnB+GnZXe4S9M2czo4JxNjU7UnYnbHvPUdkv2cs8p8RNvPQf0pMqmh8i5ak/Mx0J6Hq/Sbs28XUsN60QTqXOl9KTCTubTXHyfaeXt+Pt5HMba/E6cOTGunF7LOHjLi4yVD+UaliTqdqelhWoQUV+jnOTb1kHxBI3JdgqkkgfrKD9Xwq37WvUGV2W1w/k8DGMpekWmEURDS6u5O6tgynwQZDCRNSWpjrwJIgERpEWwlbRahLDmCRGMDvxBgxjpgdp952oWpQ6vnt0zptmUtfzCmvp/UyRg5vrH2RySWsu6icu9cTEtyHBK1qWIHmI6P1Orq2EL0+lgdOv9Jl6ylLqnrcbV1KkfYw2VTrl1kgQmpeTyOZdl5NmHk5WPXYtvFGIp4JI3tieOBCxKKa6s0Ml+JfQPm9qWkquxsdvp+0mvHx6XysLOzGrWh1bHZn0yKB5EZh2XrVc2H0u/I+eT/HusA+Z7E+wleGxtZh6XpzW24FFl3/MZAWl1RKPTqbMPplNeTYpatNM2+JlZ3xdjYzNXjIbnHG/AlkKnJ+DKq52SyC09Oo5jlnze/4v6pb/AMs1Uj/pXf8ArKTfEPV2JJzrf24mqPHaLv8ATbpez6wJM+TL8Q9WX/8AfW/uZsdB+KOpN1OmjJt+fVa3aR28j78R/qwrs+NthFyPoQmPndbbDz/kfKBUa7mM2F5mX1bpP48h0bssA148zLyo2dP9v2Yq+vb8vRpo4sRWU7BGxPN/EFzXZlWOnJXnX3M2qjXgYiV22qO0a2TqU2zelWZQuLobBxuUcn/crUHJJj1rJalpdw6/wmCiOeFXZMo19bFuetFaFkJ13Sxnq3UOntXi2rtvJ36QOn9LqwF7mPe58k+kknb2jGv+KIlHG5ezRBnHUUL6Wcotilh6AxnpNiaZWLYStlX14uLbkWkiupS7a9hLbSh1TEOd0vKxVOmsqYD9Y9cU5JMO+DzY6h8Q9Vw2uxMOhcexT2dx05HvLPwlUa+kdrUNVctjK/cpBP3lXpvxRhYXTqMTNS+nIoQVuprJ8Rz/ABrg67aMXLub7JqdCyE+rhCOCLw9Y74rwRk9He5Rq/H1ZW4HI58Slj/FzIlLZ/Tr6aXAAv8AQn3O4Z+LMmwfR0LLZT7j/wDlKXU87qvX8L/D6OjXUKxBLP4Go1dTcelqA5edR7BWV0V0PcrDYP2hCVsGr8D03HoucbqrCs2+OIZzcQecmof/ADE5NsVGbSNUFJrRxkESu2fhqNnKpA/7xE2da6bUPqzqB/8AMSosUJfpF0iARIpvqyahZTYrofDKdwz4iNA9eGL1AaNIi2Eqkh0xJEAjmNMAiVNDpi/WQSIR9YBiBOgtCkEQEBkHxJPEg+ICEekEwvSCTIFAnxAMImCYGMiDBkmRuRjAmCZJMHcRoYEzgZJg7EXBjpxkbkEyE04yJ25G5MCdOkbkwNBPVSJ0jc7GnMRBkE8SWPEAmK2E7cE+ZxMjcGhJnesgESdxdGJHmM9IuGDGTAGIQMEToyYBgMkQAYQliYA9yRFgwgdRkxMChDzB3O3DpA5Ii9whzCmTAieZ5TqGQ1+Y7bPbv6R9p6qeTzKjXlOpGueJxPnJSVSS9GriJd3oqu2yqwPWxVh6ien6Z1JctO1yBaPI955bUlXaqwOpIK88TifH8+fGmkvRr5HHjOOnuxCiaH7qEdvVQT/aVr8wlu1PHqZ63k/IU8an7Jv/AMHFjByeIdc2ODtwjMPtzENnkDVa6lVuTswZ4zk/PXWS/wBpdUaVUv2ObMvca7tfpA+db/W394E6cuXO5Enrmx1CKHpl21n82x7GXaM5H0G+k/eZckHU2cX5jk0S1y1Cyqiz0AYMNgxeRcmPS1jnQEy8bMak6blP9IXXe6/pLGr6hsE69p7HjfK18mhyh/JL0ZlVk0mYfUer25rFVJSrfA9TM70keDomSD6Tyd/ItusbkzuV1wrjiL/Sc18bLWsk/Kfgj7z1ZHE8di4t91yiutt7HOuBPYhSEAPkCei+HlY62pekc7mKCn+IthFMDLBWLYcTrszpnzr4r6vnJ1izFruaqmvQAQ6J48mavwh1W7Opuxchy9lQDIx8lfvKfx5ghLsfOUHb/wANz6ceJ57o/VrekZhyKkVyU7SreJ6iriV8jhLovOGB3ONnn0fQeq9bxOkKBeS1rDa1r5M8h1X4su6jj2Y6YtdVT8HZJOpfoxMX4vyHy3ttourAWytdMPtrfiK6/wDD+B0npPzaha1xcKGdv/Eo4dPGpnGNn8x752TWx9Hn8HqmZ04v+FvavuHOppY3xf1aizustS9fVXUf6iUejU139bxKrUD1u+mU+o0ZY+JsfGxuuPTi1qiJWoKr43OvfXx52/XKPlmVTnGPZM9107Iw+u4teYaK2IOirKCUYTUChRwNCeU+BFf8DlE77fmjX9p6wjYnjubTGq+UI+jqU2OyCbPIfFHV7DccCptIo/iEep9pW6D8OjqaG+5mWrf069ZmdY2OsZgY/V80n9vSem6P8VYONg1Y9yGoouuF4l0Y9a/xOzJTqoTqXlmgfhvoWEgbKCKCdA2Wa2Zbr+HOiugZMSplI2COdzB+JOq9N6r0wLTkA21t3AciUfhn4lPTQcfMZmpPKHz2n2gSsaMqo5E63NN7/R65/hXo7/8A7NB+nEPC+GumYOUMimjVg/KSxOpjZPx3j17FGNY59O4gCW+gfFY6vktjXUiq3W1CnYIhyf7M86+Uoty3D04lHqfUFwad8Gw8KJdE8j1qxrerWKfC6UTn869016vbM9EO8sFhcvqtxP5vcnwJb/8ApzI7N/OXu14mzh0VYeIvgDWyZmXdbtuzFpxAO3u1sjzOauNTGKlc9ky/7Zt5WvCM6uzJ6Tk8grryp8ETW6g+XnY1RwtGpx9XOjuD8RKPw1LaHfvW474f3/h+ieAx1DTBxufH3wyTacFZnkR0zpNuPkLfc/I/lUzdlbOyxhYN2SV7vlrvXvPHH41zTyKKdfvOvTRCmOI5XM+Rqof+6809u0UZ4w/GecePkU/5xT/FnUm/KKV/+Ms0wP57iL9nsraq3O2RSfcrJStUP0oFP2Gp4G34i6pZveSV/wC0AQcXrmfRkLY2RZapOmV22NRvsl/Yi/8AUPGclFI+hefWCQDIRwyhh6jc5nRBtmCj3JiOT/s9BDJLUee+MMTKyukBMYue1+50XywnzVlK8OCD7ET7I+XinzkVf/xiY/Ws3p2P0+6/WPbYB9KjRJPpKprfJ1uHyHV+HXT5gyj2nAKPAE0ek201dUpbJVGqJ0/f4AM9yeo/DdIB78Xf/SolUVp1b+R9TSUNKPwLVkJj5BcMKSR2A+/rqeuMxKvinoisK0yQvt9JAm2rLYgdCCpGwRGw4PIcpTcms0g+Ipo0xbesSRUhTDiA0MwT4MpZYhUEw/SCZUxgZ06dAAjXHMAw2gEyBIgGEx4iyYGE6CZO4JMQZEQTC3AJhGBMGSTBY8RWEgmCZG5Eg2k7kEyCZGxCTQp0AtODRWHQpBM4mCZCaes3BJg907c6WnPRJMAmSTzBJgbCkdIJkEwSYujEg8wgYsSdwdiDQZIOoAhQqRBoaEDFA6hBo3YgwGSDADSe6FSBgyduBudHUwDAYUUDC3HUgYHCBi+6EDGTFDMTdi05K6sQH7+sbuSDxDOEZxyQNa8oyLehKW3VaQPYiTR0PtsDW2dwB3oes2BC2AN+0wP4zixffqP/AJE/WlfItKoK1445lUQnJsc68k8SXr1YtY/NrmeM59tnKslJel4GjkfAHkSNSHPyye4gCVbs2taLTW4LhGKjW+dcf5znQrbkost84RV1Xp12c+DXnYz5afmpWwFx+0tz5R1L4Fst+DOmdW6TjuPiajsyGev6Xsdj3MG2fI3/AJan1LFa58Khsley81qbF3vTa5/znQ5/DoogpUz39P8A8f8A8K4uTfkbPMda+Oun9G6o/T/wmdm31Viy8YlXeKV92npz4nnOkdBycH4w+Ier2lDT1AUioA7OlGm3K+BDjtylyPKS8L+ySb/Rs9L6nh9Z6dTn4Ny3Y9w2rr/ofYzZwbAwNL8g+88dgYtPQvxdHT17KLshr/l/yoW8hfYfabnT875lgJ4YeZo41kePy+1X8H/+gzpk4aazdDwWcsahzHp03ErAC0Jx7iWFsBQNvzKl/VsOjYa9dj0HJnt/q4sV3aS0xJ2PwWRWiDSqB+k4iZZ+IcHfmw//ABmjTdXkVLbWdow2DGhdTN9a2RwmvMkcRM3rPU6ukdPsyreSOEX+pvQTVInmvi7oeT1jDqGL2m2pi3ax0GBE18aMHalZ6FlJpeDwq5ud1lM8ZNz2D5fzu0+FKn09uOJnYGL+L6hRiluz5rBe723PddA+GL+n9PzXy1X511TIFB32jU8d0YdvXMEb/LcvP7z1XHvrycafSMMovU2ep6dm9D+Gxbjfi2uvLfxGWs+R6TH+JuvU9XNFeMti117J7h+Yz0nV/hLByrb8lLnxmJLMVGwf2nzywKtjBW7lBIDa1sTPwaaL7Pu3ZIstlKC6j8HLbBzacpEDtU2wp8GTm5T52bblWBQ9p2QvgTU6D8PHrNd1llzU11kDYXezrcVQvQUzDTccyyoNr5oYAfroek6DvpVjaWyRR1eC+jdVzen5tSY7sa3cBqtbDb/8z6nrYmN0/oPR8V68nEp7z+ZLGct+4mjm5tWDjNfcwCLPK/KciF9q+tYzqcauUVn9mR134dHUXGRj6S/WiD4eeTv6L1Gg6fEs8+VGxNjL+Mch31iVIi+hfkmek6D1Bur9P+e6BXDFW143M8ZWVx8nXVt/GgnJeD502FkopZse1QPJKGIH/wCbnvvi3PTGwPwqEfNu4IHkL6zwZH95orn2WnQ4t8rYOTWGnh/DfU89EsppX5bjYdmAGp6/4e+Fm6Vk/ir7A9vaVAA4G5odL+X0/wCH6HuPYtVQLE+kxj8eUDI7fwbmnf5w3OvfURyb8I5NvI5F/aEF4PYieW69jtVnfO/lcD+89Dh5dWdipkUN3VuNgwM/BTOxzWx0fIPtObzqPur6/tHPpn9U/J5rJ6lflUJT+UDQOj+aavRumfI/9RcPrI4X2k4XQhRb829gxX8oHiR1XqoqBx8c/V4ZgfE5tdTrX28j9ejROal+FRQ65l/icsVIdrXxx6mbvTcc42DWh/NrZ/WYnSenPfb8+0fw1OxvyTPSg6Ev4MJSsd0v2Jc0oqC/RFiLZWyMAysNEGeUz/g+t2Z8O3s/6GHAnqS8DunWckzm8ng1clZYjwNnwx1NG4qRx7q4/wDMWPh7qhOvwpH/AMhPoBIEAncDZzP/AKb4z/bPDL8M9Sbg11r+ry9h/CbrelmVcpVTsog8mep8TtwaX0f+nuLVLt5Zw/0E8B8dLmjOrsJsOJ2aUD8oP3nvhF3rS1TfPClNc93iJLyei401TJPNPjIPE6b3xJkdLfINXTseskfmtXhf2mCP85ne7h6qiUZw7uOE63Om3UmH0ehD1HCbIvuXvVPHavpvc08Lq/w4Tq3AWk/9dYI/vJ1KZ8p+4x1HkNEnQBJ9tT6l8NU5FHQsdMnYsAPB8gekfhVdKvVbMSvHYehQCaGgBGSw5HM5f3Lr1wE+IARnbSgniEeTLVI7V1GUe3g5s59ShbW1Z03mJPiXMwl7PH5RqUyZRbHqy2uXZaCeIH2hmAfMz7paQRIkmR6SABYwD4hNB3IFAMeIBhtAPgRWMiDI4MgnmDvUAxzHUAmS0EkQB0EmAxkkwWMhNB3BJnFoBMJNJ3IJg7kEyYDQ9ztwNztwYHQ9yQYvukhpMJp6udOnTY5GMEmCYRgExewyOMGSTIi9gnbnCROi9iDBJHiK3CDQqRBkkGL7pIaN2INBhAxQMLcPYgzcncVuTuMmAaDCBixCEsUiYGDDBi+ZIMsUhWgiwUEnwBueeu6hfZczJYUXfAE3rQzVMoPJGp5p8a2pu1q23+k5Hy1tyUfrNHGUdfY0cTrLowXI+pf6h5m2XVqO9TtSODPJpjXWMFWttn7T1FNRqwlrPoupVwuTyLKpxs/SByYQi04sXiJ8zIG/CjcYDvMt+0Ppw/it+kBj2Zlm/Wc6utV8SM3+5PTNJ7PDE6mxCIATyTuZvPvNzJoW9Sre/BlNemkWfU/0ziucU3p0apxUPJYwWZ8fbEk78mY/xp8QZHwv8PHqlFCXCu+tbA3gITon9f8AeegVQq6HiKzcLG6jhXYeZSt2PcpSytvDCSi2uN8ZWrY/szWPXqKHU+s42L8NZPVVyq1pGO1td2wRvt2uvc70I7oWdb1PoHT86+v5duRjpa6eNEjmeZxf+Fnwzi5a2hM2ylH71xrcgtUD5/L6/vPagAAAAAAaAA0AJp5kuHGvpx2293fX/gSKf7M7KwnZy9eiD6esbh4zUbZvJl2dqc/7X6LnY+uFnqDZH+DbxwS+gDrzr1nlSe7zvY957nDXeOv6QMjpmJkndlS79xxPbT+Ps5dEJp/peDNTyI1SaaPD6nsuigjplQI1xA/+nsEOHIc6O9d3E0kQIoVRoDwJZ8b8bbxrHKwnJ5StjiRJEHUPxBPmdloyIRaD2MPcET5R0HEsv+J6a0XfyrSz/YAz64w2JVqwcah3eqitHc7ZlXRM18Tm/wCPGSz2LOPZo878ZZz4vSvlJwchim/Yes+cBGZgqglidAD1M+rfEHQx1rCWsW/LsrPcjEcb+8y+i/B6dPya8rKv+dah2oUaUGdH4/nU8fjvt/JlNsXKRo9F6d/hvRa8bj5hQlj/ANRny2+t8fIsptUq6khgRrXM+06UekrZHTsDKt+Zfh02P/UyczLxPkvpslOa3SWJOKSMf4LpsPQF+eW/5jdgP9P+0ufE3S7M/o5rxhuxGDhd/mmqpVFCqAFA0AONCF37nPvuU7XYkWVXSraf9Hy7E6F1HKvFQxrKxvTO66Anv0Sn4d+H3+WvctKd3J8maZO4u+lMjHsosG0sUqw+0SXIcvDNt3yEr2lL0fKczNuz8psi9tu39h9po/D3SX6p1FCR/AqIZz/4mm/wPb+JPblJ8nfqD3anrem4FHTcRaKF0o8+5MvlfGMfB0r+fVGnpV7K/wARYtuT0LIoxx9XaND3AM+YlHFny/luXJ12gc7n2LcWMejv+YKk7/6u3mUV8jr7MHE5/wBCazdMz4Vwr8HoypeCruxftP8ALv0m6DFCFEduvTHZNzk5f2GTsGZC9BoFxcuzAnepqQhqJOMLc7r0SM5R9ALWtaBVGgIJMdqLdD5EtSSWIKYkmLZtQ2BHmKeK5YWxILwS/PmLZoJaL3LUhvdJ3EgwgYVMmDNzxnx1lZKNi0KxWh1Yto/mPtPYAxGZgY3UKDTlVixDzo+kLeotomq5qUlp8jALkKoJJ4AHrPZfDfwuysuZnJojlKz6fczfw/h7pmBZ8yjHAf8AqY7M1QNcCKom7k/IucetfgzOsdCxurYwR1C2L+Rx5E+d9V6PldIu7L12h/LYPBn1mIysWnMpam9A6MNEEQteDPxeZOl4/KPkWLmZOHeLca5q3Hqp4/ces+m9J6uuf0xL7FZX2Fb6SATrex7iZQ+CcGvKFgttNYOwmx/rNk4oqrFVNS10oNKqDQAle9UT5LlVzSlBGgpDAMPEcGIWYteVZVdon6CeRNE28cGNXaktZxVOV0vAVlnaCfWUmjWJMU0z3W9n4OjXDqsBEg+ZPiDvmUaWHSD4kmCfEJADBJkkwTE0IDGCTxOJgk8QhIMAtOLRZkDoRMWWnMYsmAnYkmLZpxMWZEgaSTBJkGATGwHYMmQWgEyCYcJofdI7oG5G5GiaHud3feBuQTFwOntSYJMEtBJkdmmYktB3BJkbi9woLc7cEmQTxD2DoXdI7oG5BMGh0Z3SQYrc4NGTCO3C3EhoQP3h0g0GEGigZIMZMg3ckGL3CEZMg0GEDFCTLEyDgYQPEUP1hgx0wByR5i3sWtC7cKBszPbrKA/TWSPSV28iqr/3GGMJS/ibKgH0jNbUiefPXLAfppH7mOx+uk2AW1AKfUHxKf8AUeK04J+wT49nto1MI/LymUw+oVacWr68GKyFKOmQnrzLld9WXV2nz6gzn11wnVPhzeP2ihv8uxkESCJZyKDS3uvoYieU5HHsom42I1RkpLwROnTpnCdOnTpCHTp0ZWUq/jW8VpyZfx6XbYooEn4NnGTsx1HrqNnm2+JmDEJQO3023MdR8TUMe25GQ+/kT6PRz+LGEa1L0ZHx7feG9OiacmvIQPUwZT6gx06EWprYlDTXhkGRqFIJiyRNAIi3YIOSJF2StewOTM97C3JMpaFlYkWXu2eBFGzcR3ceZ3dA3hU5aO+YZPzDK/fO74jYNLHfCDSuGhB4jIWO6SHiA8nui4EsA7hAyuGhhuICaNBk7i9/eT3SB0aDJ3FgwtyDJhgyQYG5IMZDJh7kgwZIlkZYE5kDDmVbKmHp+8uA7nEbEsaUiyMsMhwREE8zTuxyW2o4mfahVjM8ouPs0RkmADCDRe+ZMrTLENDcxgMQDzGAyxSJg3c7cHc7csTFwInicDB3I3A5EwPXc2hHGvtXRlUWFWDD0htlM/nX7QwlH9lVtcpLELupq2T2DZ9ZXO1P2j7H3FsPp3KrEt8Eqp6r/kHvBHEEncE7kcylwTWo0pnepkE8yTI7j2kSnBgSeIJPEkwCYGQgniLYwieItjBhEwSYDGSTwYomEmkkxZacW5ii0KQGySYsnnzIZotmjYDQiYBMEvBLQpA0ktAJnE/eAT94cJoW5xaLJkbhBozug90Hukd0GE7Bd8IN9oruEjugwPY9sTAJgFoJMxdisImDuCdztwqRNC3I3BJg90bQ6HuD3QC3MDukTBo7uhBpX7pIaWJh0sd04PK/dDDQk0sBoYaVw0MNDodHgwgYkGGDGTDo4GFFKYwGOmEYIYiwYYMsTIBlKXxbFA2SvE84TzPUgbEyszpNnzDZSNqfK+05XyfGnbko/ov49sY+GZcJFLuqqNknQEu09JybH0yhR7mbmB0unEHdruf3M5/F+OusknmIuu5MILx5LWLQVwkqtPce3RMp3UPQ/G9ehE1ROKhhojYnoOX8bHkVrq8kjkfY09MoZNoGidj7iKOyd9p/YS/f0/f1VHR9oj5l2ONOmx9xPM8jg8hS68lvF+y6Ni/+0ranSw2Uj/mpXf6xDsrHar2j2nN5HHrr/hPSxSb9gzp3rr19pYpw3tP1Aqsr4/Euvl1gguaXsVVU1zdqDcZ1fEYdMArXu7GDMB6zVppWpdKP3jSNjRE9z8f8FCql9v5Myu99k1+j52DI1zPR9W6GSxvxV88sg/8AE8+ysjFXUqR5BnnOZw7eNNqS8HZpvhZHUa3w/eyZwq2e1/InrZ5X4dxWsyjfohEGv1M9STobM9Z8Kp/42yORzXH7fBJlDJzOSlf/APFF5WYWJSs/T7ykSJ0pSMMpB9x3AJgloJMolIrYe5HdB3I3K2wDNztxfdO7oCDQYQMTuT3QMmjtwgYgHmGGgDo5TDBiAYYMARwaGDEBoYMhEPBhbiQYwGAbQtwgYG4W+JCJhgydxYMMRkOmEDCBgSZZGQRg5lXIxwdlf7SwDC8y3FJYPGWeTDspKngRfI8zWyKfUeDM+yvR+8ySg4s1QkmLHmGIIWEBIiwIGT6yBO9YwSZB8GQYJgYCCYEMmBuVMZEGDuS0XuI2HDiZ0iTuL6ICTzInHzBJiMmkEwSeJx8QSYSAkwCZLGKYyE0hj5iWMJj5iWMKQNOLRTGSzRLNGSF0ljFkyCYtmkA2ETALQe7iAzxkLoRaAWglostGwmjS0HuEUXglpMBo/ukbig87vkwmjSwEjuiS07cPUOnty3EjcEmRuchMgW5BOjBLaEAtGRAy0AtF90EtHF0MtzALwC3MAtGRNHd0nuiO6SGliIO7oSvEhoQaHQ6WFfZj14G2Ot+JXo0bBv8AKOTDLFm2YGyaWVZPeGGU+CJVUwxqRMOlrWoQiq3Gu0+I4eZamHRijcaoilMastTCOUQxFrGqI6eiMJRzHqdiKURqjiWxKphMy1qWY6AmW/xFio5X5drAHXcANSp1vqWz+EpbgfnP/iYW9Tk835Z0z61Gyjh/Ytl4PeY2VVlVCypu5TGvWH4IBnm/hm1vnW1fykd36T0d9gqoew/ygmdficiPJoU5ox21/XNxQpsGk/y/2gf4dT7H+8wl+Jr/AJwLUp8rfgeZ6ai5cilbUP0sNiU0VfH8mTUYrUScbILZAJi1JrSDYnneu5t9eeKqrGrVQD9J1uepnlvieork1XKOGGjJ8nQqOK3Qs/6H4mTtSkP6T1x7LBRkn6j+Vveeh8ifOgzA7U6I5BntOjZ/43E+r86fS33lPwvyTt/2rH5Lubx1W+0fRoyjm9Jxc3mxNP8A1LwZoa4kandsprsWTWmBSa8oTRRXjVhK17VAlDMzDYTXWdIPJ952dmd7fKrP0jyRKJPEqbjBdY+iucmySYJaQW1B3xM8pFZO5Bkb5kE8ytsGE7nfvB3I3ABhToO53dIAPckGBuSDIQYDCBitmSG1IQcDDBiQ24QaQg4GGDEBowGBjaNBjAYgGMBgCOBhAxQMYDJgQxJBkAzt8yIOhiSDBBkgxkNoQMIHmCJIlkWMmGeRqU7qQD+stgwXXvUj1lrSkiyEmmZjV6g9st2VkeYkrqZ3DGaVIVriRqM7ZHbJg+gagsIwiCRA0FMURAIjSIthKnEZAN4i40+Io+ZW0EicTOgk8RCHEwSZxgkxQEE8wDCMWTAAF20Iljsxj+IowoGimJiXMYx0JXdowGzmaJYyWaKLR0I2QzRbNId4lnhSBoRb7wC8AtuCTHwGhM0Du+8BjFlocA2OLCCWityNw4DR3dO7onund0mB0b3Tu6K7p3dJhNPdziYJbUAmcJFmhE8RZM4txFlpYhWziYJaCTwYBMsQAiYO5BMEmOhdC7oQaK3ODRiaODcwg0SGhAyB0u0nVbN78Qw0TS38Nl/eSDIw6PBhgxIMYDAg6OU+JYQ8CVVlhPEeLGLCxy+IlPEcviWph0csapiVjRLIsVjlO4nqGScXCstX8wGh+sYh1JtqS+s12Dan0jWa4NRF8J+TxR2SSeSeSfedNbK6Dejk0HvU+B6iKq6Lmu4DVdg/qJ3PK2cK/u01p1Y8ivr7L/w1Sd22nx+UTdy6zZi2oPLIRE9OxFwsVagdkck+5lwjYnr+Fx/r4yrZx7p97HJHzvt0Sp8jier+Hb3sxWQrpUOgfeMs6Bi25Btbu7TyUHgmaGPjVYydlSBV9hMHx/x1tF7m34NF/JjZDqkPlLqOCudjNWeG/lMuyJ6C2EbYOEvTMMW4vUeTT4byzZp3rCb8iejwcOvCoFdY+5PuZZM4THxfjqOPLtBeS2y+diyTJmfnZet1Vt/3ERmbk/KXsX87f5TILcnc2WWYsMzZzHcAmcW58wSeZjbK35O3I3I3I2JW2QnfMgyPMjcgGTuRudIkFOJnb5kGRIQPcIGL3CB4kEC3JBg6nAyE0YDDBigYwGEbRgMMGLBhK0VhGgxgMUIQMgyGgwwYoGMXxIEaDCBgCFFCg5IMGSIyCGDJgiFuNo6JBhbgSQZZFjA2p3DfqJXK/aXIp153LGtLYSKpWD2x7CARB1LkxXbBKx2oJERxCmIZYthLDCKYSqSHTK5imHMsMuuYo+fEokNooiCRGn9IB8yomgGAYbedRZiEIY8RRhsYsmQDYDRDkxrGIsPMKEE2NxK7mMsPmVnfUdAbIcmJZpzvs+Ylm+8sSEbOZjFMxnM0WzR0LpxaCTBLQS0dE05jFkzi0DuhF0PcHukFoBaTCaN7pBaK3O3JhNGd87viu6duTCae/LiCzxAaTszg4WtjCeIsmTuATHQNOJ4MAmETxFkx0AkniCSAJBaCTuWIB2zJBgb5kgwoCGLGKeYkGGpkCWqT9YHvxGkaMqq2juWXPcA8gQxGp4ldTGoYAoessJKymPQxkOi0hjlldNxwMsRBwOjGqYgGGGjpkLCmNUyuGjVMdMVoeCIYiQYwGWxf7K2hghiLBhCaIzK8CnCROlqmAKdI3OlikQmJyL1or7iefQRjsEUljoCYuXeb7N/yjxI5+BH4FvabGLsdkxRM4niCWmactYhDQNyWaDK2AkwSZMAxdITuRuRO3JopOzOB5g7kgwisIyJBMjcgApIMGduQUPcgGRuQIQDQYamJEMGQI9TDGolTGAxWFMaDCBihDEgyY0HmNUxAjVkCOBhgxIjBBo2hiTBEISBQQMkeYIhRkx0FJEESYyYyDE4jYgiFLoyGTFMsWVlhhFsI5ZFidQSIwjiCREaLExJEBhxGmLIlEx0xLCJYSwwimEokWISRFsI4+ItpSwiWi28RrRT+JWABvEQxjCeIhj5kQjAYyva0Y7SrY3JliQBTtwZXcw3be5XdpYkI2LY8xbNJc8xTNLEhGyGaLLSWaLJjJA04mLZpJMWWjA04tA3OMHZhwXSe6QTBLQdxkiaGTI7oGzO2YcImH3Tu6BsyNw9Q6e77pPdEh5PdPPYWjNwSYPdB7o6RNC3BPnzO7pG48UDTj5gztyDHQpHrJEGTCiaEDzDEWIYkZExymWKWB+lvBlUGMVoBtLOiraMYsCp1tUI3DDwfeHoqdGKMh6x1Z4ldG4j6+IUx0W0I15hgyuGjFMsTIWVMMGIDahB42kLAbUYryqHjFbcKYcLSvGA/eVQ0arS1MVxLKn7xgYa8yqGhh5YpFTiWAZO4gPC75YpC9Ru5IMV3xWTkfKp4/MeBLIyFfgrdQyO5vlL6eTM8nnUJjzsmKJjSl4KW9OJgEySYBMpbFOJnb4kekHcVsgW9QT53O3IJkQDtyCZ0gyCs4mSIM7cIpJM4GCTOhAHudvmDuTuEAW+ZPpA3C3AQIQgYG5wMIBymMUxKmMUwEHAxgMSDGKYBkNBjFMUDDXmKx0OBhjxFrGLE7pDYGJIkCTF7DpEwoIhSdmOkSJO5Akw6xkSDJ3BEmOpNBD8wWE4STyJprs3wwiT4iyY9hxFES1lkRZEWwjiIozPMsQphFMI8xJ9ZnkOhJHBimjWiWlLCLaKs8RjGJc8SthFE8Sq58yw5lZ4yEfsRYZVsbkyzZ4lSzyZYhGIY+Yhz941vWV7DHiI2LY/eKJhMYpmliQjIcxZM5mgd3MsSA2cTAJ5kkwT5jYKcTBMgnnzIJjAIMGcTBLSJECnSNztxsBpMjW52524QpnsA0nvlcOJO9zhOJdo7vk90Rv7yQZOgNHd07uitzu6MkQZOi9ztwgD3O3F7hAwogYMMGKBhgwaQaDDUxIMIGAI8GXK7hYAlh03o0oAximAZMv8AYyHn9vvGI8rU3lPpb6kPofSPHYeUOx95EOiymyeI4MBKq2AcQ+77xhh/fzCDSuDDBEKejJDw0YryuDDDQoZItq8MPKqtGBpYiOJaDQw0qhoxXliFcSwGkh4nvE7vBjaJ1HmwKCx8CZt1pscsT+kbkWfSE3+sps33mmKyOsyWy/SOY8+YskSS33iyefMST0pRxMjc4mCTqIEktI3IPM70kAcTAJ5kmDuQUmTIkbhFZJM7cEmduTRSSZ25BMjcgoU6QDO3GRAtydwdztwkGAyYAMLchBqmMBiAY1dnxBoRoMavMBFAHMcupU5jRQSrHjXtEgximVyelqQziMEUDGAxAhgyYO5IhGQQk7kToUFBAydwdyDYo8sBDo4ydFG9F/m3+k4ZCHwG/tDoR0IGJ+cvs39pwuUnw39o8ZYwjGEUfWMDq3rBce01xmmhkxZimjSNRTSuRZFi2ijGtEmUSHQpj5iH5Ma58xLHzKmhhTeYl/MaTsmJYxMCKbxK1nrH2eZVf1hSFYmw8SnYeZZcSrZ5jIrZXc8GVn3H2HQMrOZciti2MQ5jXPEQxliFBYwZzGAWjIBJgmdvcFjGQoJ8ztzoJMZIhBMHe5zGCDCKFszu4wdzoSE7M6ROgIemDwg8QDC3OS0XaO74QeI3JDQYQeGhd0QGhAyYQb3Tidxe53dBhBm5O4AMLcGECBhAwBCEhBgMMGKEMGAg0GGGidwlaQKLKtHVtqVVbiNRpMHTLgaNVpVVtxqmAsiyyDDBiFaMBjJFiGgwgYoNCDSxIZD1MYGiAYYaOMh4aGGlcNGAx0Bod3Ti2lJi1PPMCx+SBL4V75Zkus6+EQ7biWMkmAxjyZi3SCYB8yd8wSeZU2Q7cjYkGD6waAkmRuQZG5AEkyJBM7cgp252525BMICZ24O5O5BWcTOkEydRhSRJkAztwhJnTp2pCBCGIAhiBsAaAbjl4iAYxWlbYyHBoxWiAYxTEGLAMIGJBjFMVjpj1MMHiJU8zmvRNgnZ/pXk/wBooxZDe85rFQbZgB7mVwMi8cD5I9yNk/7RyYtYbufbt7tGUWwkfiA4HylZ/v6QwMhj5VR9ozXtCEsUApi1o3+d2aNFSL4USZMsUENpwUDwBJ1Ikxkkg6TJ1InR1gdC7VPkQTWPQyQZIjpRJol62HmJYcS7AaoMJHXvoeMsM9hEPLt2O6glRsSk48zNOLi/JcpJld/EQ/iPYRT6ErY+lcnUQ5lhiPeV3EXCaIc8mV28R1hOzFFT27PAgSBpWsMqWHzLthq1osf1ErWLjkH+Mw/VY+FTM+zwZVc8y/bjFlJqdbAPbz/aZ7jR0Y8RGAxiWjG4izLEgC2i/WMaLPBliQCNnUHzJMiNgASYBMljAjCnEyJ06QB06dOkBp0kSJBkwhv7hBouSDqcrC4aDJ3FBoXdDhAwYYPMSDuEDFaIO2JPEV3Se6DCaNBkgxXdCDSYQaDCBigYQMARoMLu5iNwg0mEHd0NTEAw1MmELCtGq3MrAxyNJgyZbRo5TKqNHodmLhYiwpjAYpYQMKLEM2IQPMXuTsy1FqQ9TCDRCtGAxxhwMYpiFMb6R4LSu2xQjofdIYgwC0EmXdkjlSbk9ZxMBoRMAxWxQTxBJ5ktAJ5iEOLQdzp0hDtwTOPmdIIzp04mDvmEAUgiCTzJ3xCQ7c7cEyRIKyZ3cZw1JjaKcCTJgwhJpAhJgydxWwpE7hAwNyQYpMGAwhAhiQZDFMYpiPEI2qnn83oB5MUJZBnG5VcIPqc/yryYpK7bSO89lfsPJ/WW6akpHag0IeujaQtF12xa3y0P8qHn+8t001VD6EAPv6mCphhuIeiRBwhCLB4hKY+DIOdB3zJBk0IW5IgzgYdGTDnbg7k7h0OhbkwZ0mjBSdwdztj3jJkDBk7i+8DyZxtUfzD+8KtS9sIzcTbjV2+mj7iT8+v1YSRdX/UI33VP2xlplZOJZVs6JT3EzXIJ3PUfNQjyDKWX0+nJHdX/AA39x4MrnGuXmLHjPPZ5t28xXzNHRlnMxMjEY/NTSejjkGZ7nXMocWi3sn6GWIAC2+JSutL/AGX2jLLCRr0lV24MgrYlzKztsxrtKzmFFbYvvKP3KdEeCJGV/EVbgOTw36wWMI7/AAb/AGYR0gFN4oxrRLR0KKbzBPiGYJjoAG9wdyZEdABIgEQzAPmEAM4CTOkFZE6cYMKITuQZ07RhwhvbnTtTpyS46TuRJhZCRCBgiTAQOdBBkiTAB7kgwZMjRA1MIGK2QYQYxcCM3O3B3O3IQYDDVooGGphwiHKY5DK4J3GqxhwJbQ7MsIdGVKjzLKHZlbLEyyphiKUiGDIvZagwYW4Inb5lqLEwxDB+8WoLHQBJPtLlWKFH8Tk+0thBy9AlaoryQitrfpCJjbfy8eAIgnYl7h0Ofba7GQTILSCeYJMzyZUTuT36+4gbgMeYnZoIxgp8cQCp3OB4kd0bsKCeJBh9wkHRjJoVi5x8SSJBEICIJPMLXtI0YNAcZE4+JG42kbOPmdI3zOJ4kEDB5nE7gDZ9I3QHpJpDgvGyZIkhCfSF8mwglRvXOhAQCdBDCFIQ6EIPpCHiQIY8Se4KNsdCJ+aSe2pC7f5D9Y+rGG+61u9vOvQQpaQFTZdr5f0r/Uw/0EuU1LUdqNn+puTBA5jRGUcGGhtxgiRDB1GIPUwwYgGGGgGHAw1MSGEkNAEfuSDEhoQOzAwobuduBJBi6EMSYt7FQbY6mZn9ew+n1lr7VUfrz/aLK2MfY8Yt+EbBYART5NafmbU8DmfHRsJGJUdejN4mNf1nMzSfm5Ddp/lHAmS3mqPo6VHxl1nl+D6Rkdew6T2/OUnxxzIXOuuG6wO0+u584pt9zNzpfVXxmCk91Xt7Tl3c+5r8TXZ8W4R1eT1ROQ55tA/ad8onza87HuTIrFlZ2pjZy58y5vyzH0S8YL+T/wBbn94QRdeW/vDPiQIi5Nm+yYgPqHhjGJbYv82/1kSJdHlXQepg6pllckMhS1QVPB3MnP6Itqm3BYD/APxH1/Qy7JSx6vyePadXjfKy3rYI6/6PG2BkcqwKsDog+kq2HzPXdZ6emfinKpXV6D6gB+Yf7zyDHYnchKM1qK34ENK9ksNK9kdFWiG8RjnswtE8sdyFr72A9PWBk2Bm0vhRoRyaVjzFMIZMWx4MZIGgGCZO5EdIgsjU6SfMiMkAAiARzDJgnzCAHUgnUL0kEbjJCsjzI7YU6HCA9snxJJ1BJMOEN7U4CFOA3OLpcDqTqF2wtQ6QDU7UIzpEAicPE7UmMQ6FBhSEOkiRJgZCdyQYMkGRIgwQwYsGGDCQaIxTFAximQOlmuWazKiHmWazxEkPEspGCJUx1YZ2CoNsfSCKbfgsTwMeJYpxHt0xPav39ZZx8Na9M52faWCZvr4/7kVT5GeIkV1JUukGvecdSCx8QO6avEV4MkpOT1kkjXMrMPlkkfl/0joBlM/yAmKJHkQSZz1EHur/AHWLFik9p+lvYzJJPRtJJ4gwt7kalYdInGd6yDCA6QZJMCQUmRucTxB3IRhd3sZ3cYM4+IdEJ7h7TvpPkSACfA3JFTnwI3kGAsmxtTuSlZbzxCCdvngx6ugAHdzGimwquTWpHLWFAAEnx4Akggzo/UTGvZ0djELerE6A5J+0TKXUs1aEGOp3baNaHosV+AAJcHLEeCxIjlaUUbsXbECWKzbaP4a6U/zt6QLyTR7Wqg5PPt6zkSy3l9pX/T6mNpoSvR1tvVj5lgKNRlEgNaBF0o0I1YIHpCEdIbAhDEASQYRhgMPcUGhd0BBoaEDEgwgYBh24QaKBkgwBHhoQMrlu3ydSh1LrmJ0ynvvuVB7nyf0iSkl7HjFyeI2HsVF2x0JidV+J8Dpafx7grf0g7Y/tPBdY+OcrMdqcFTVV4+Y3LEfaeZJe2w2Wu1jnyzHZmd2N+jp8f4+U/M/R6rqvxxm5p7cIHHr/AKidsf8Aaeea+25y9tjOx9WOzEBftGKplDg2dqiiur+KH1sT67llGIlepTHoplUqTZGwt1WH1l2m7Uz0EtVzHZRhY5JnouldTfFcAkmpjyPaesrdbEDoQVPgifPqH009F0jO+URSxPaf8pzr6f2kcnl8dP8AKJ6GdOB2NzvMydTl+jp2pOpMs9hQELXE6dI44QhHNNob+U8ETx/XMUYfVba1Gkb61/Qz2Djamec+KucnFY+TTz/ed74m5v8AB/ootWeTzzEaiCpZgANx4QuQBFXXLSpSs7c+T7TvRRmbAvZaE+UhBc/mI/0lBmhsfJPmKYyxRA2CT9oomGW5iyY6QNBME+JJMEnYjYTSPSQTOJgsY2E0gmCTOMiMkBs6cZ0iTBSZ0iRuHAkwTO3O3DhD0mpwELU6cE0EanQhO1IQAzoWp2oyYAJ0LU7UOkI1OEmdCAkTpEL0kAROEkSdSBRIhiAIUKAMEYsSNxywkHrLCHiV0j08xGWRLCk7AGySdTexMZcev3dvJmNha/GVd3ju9ZvkkTXxIJtyZXdJpHMYBaSTFkia2zMFuRI2JBMrbISYB8TiYO4rIzjxFWVrYNMIwwTEaBpWZLaz9B719m8/tBF693a20Ps0tGAyq3DKCPvK5VhTF92/EgyDjhea3ZD/AHH9os/PTyi2D3B0f7StwaCmMkGK+em9MSp/6hqMDKw2CD+kGBI8iRO3OkFZ0alW+WPHtOrTjuMZ6R0hQhpRpfEB7Ai7MkTPzrijgb/aFvqX8ev7JqJZ+b3nfpHE1CnfrMcZBHrxJOWdeZZC6J3v8bFiLfzmQ7Blyi0Wpv1mC+Txy2v3mj0dzZjlj6mFzUjm/IUqMVL9j+pdQq6bii5+WY9qL7mebxrLsrJe9vrtfg+y/YT1XUKK8jo2ZXavcAnePsQRMfCqCAgADXEr66zjy8D8fEVSGsJcjwD4H7TRU+kWi8RyiWZhEtDWGIM7cI6QwSfWCJMAwc4SNzpCBTgTBnCQg0GEDAWRZalS9zHiBsdDgYjIz6cZSWYbHJ+0851z4sx+nV67i1npWh+o/r7T591Lrmd1VmFrmuknipT/AKmUOe+jVRxJ2/8AR63rvx2vcaMAfNfx3/yr/vPFZORkZt5uybGssPqT4/SLrr3wBLNdJJAlag37O1Tx6qV4FKhPiWK6j7SymLr0lunCdxsA6/SWKGFztwqJQSORHpjmbOL0e19fQdH1M1avh/kbjOKF+5o8ulGo9aOJ62voFf8ANoxx6DSPAi9UT/IZ5BaOfMctZE9YvQaiOANwH+Hz/LqVSrTGjyl+zzta/VLtDFW3LVvSrajwmx7xApZDyJit45oV0ZrD0vTMj51PaTyJogTy+Dc1F4b09Z6etg6Bh6zjX8dwenJ5FfWWonxIhakamdIoOna3JnR0t9kBI4nmPiY9+bj1D8yVfV9tmepA2eSB9zPn3V+pHLz7rV4UnS/oOJ2fiKn3cii+WIr5GQKlKVnZPkzOawkknzJdvpJJ5lct956OKMjehM24tjBZvvALfeWJAJJ5gnxIJ48wCfvGSJpx3BJkkyISEeYLQjAMKRCDInSDGISfEHckwYCHbnEyJEbCE7kbkzoSHqdTtTtyRPPJGoidqTqFoQgA1O1CInakIBqdqM1IK8xkAWRIhssjUmkYIhanahAQ6AgCEBOAhASaQjUkCTqSBIgEqIxRBAjFWFEG1x6RVY5j1EDHQ6vasGB5B2JuVXi+sMp5HBExFHEsUWNU4YefaPTd9b8knHsjW3uAYNdyW+OD7GEeJt7J+UZWmnh24JaTBMgCCZEgztwCEkwdzp0hDpEmCYCEGDJMiD2DSCARogEfeIbEp19KlD7qY+QYHFE0rfh3T8lzH/uG4pjlJ6VMPsSJdMW43zFcERMbRaLaQddrDysZqZWQrL9SMVI9RKNvV8/FGgi2D03xK3PqPmnoz4mZ1Sosnco+oQOi9UyepG8X461ivWiD7zQur7wQYH+cSyqbqn2PJNllT5gHNOvImtl9AqydkO6MT5X0mdf0DEwMd8jMyrWrTyD/APm5n6SR218jU1v7K9FludkCmgb5+ptcCeywcZcXHSoeZ5rD630/EqFeNj5Hb6AVEf5mOt651LMDJi1LjIf/AHD9Tf7S6Ejk8vkO5/8ABs9U6ildbYFLd19mvmD+hfY/rFYyH+8zsDBarTMSzsdszHZM26q9NszRH0YJLRyDQjBBhLGGSC1OAkzh5kCEOJMiTIE4QtwZMDITJA0NnxE3ZNWOvdY2vtPJ9d+MqcYNVUQ9muEQ+P1MrnPPCLK65TeRR6TqHWMfBpZzYo7fLHwJ8+6z8Y5GY5qw2dU8G0+f2nn8vOyup3GzIc69EB4ECuqV+Zezr0cGMfMyCGtcu5LufLE7Jj66Gb0jqKNmaNOKx/l4lsazc5qKxFfGxSfQEmamL0x7G0E595d6fhAkEr49J6bGx661BC6Oo+YVSt0yMXoZDfXrU3Mbp1FKa7BuWF+wEcikmIxNIrqVR9I4jgOJKroQgIjFbOURwAMVqMXcmCtjFUbjgoiF3uPBjKJVJhmlH8iU7+k1Wg6GjL6mNA4hcExFZKPo8tf022gn6SRLvT7SF+W3vNwoGGiAZWswkLd6DtYTHyeIpxeFv+R3WSAkkcSRxx6ySOJ5yVbi8ZXovWpOoWpS6n1KjpWI2TkNwOFQeWPoBJCuUn1XsjaXkzvibqa4OB8hH1kXAga8gepnz939Nxuf1C3OzLMm5tu58eij2Eos+zPW8Hi/TWl+zn229pDGbY8xBPPmcWOootN6iVphEwC3MgmDGSDoRb7wd7nHUiEJJMjcg7kQ4QkmDvzJMGQJ0gmduQTCQgmRuduDCAKcZG5B8Q6E7c4GCZ29QEPWCGItTDDTgGkOdIBnbkITO1OEmQhE6TOkIRI1C1O1CQHU7ULU4CTQHBZPbCAhBZNIABDCwtQgsmgwEDmMRZwWMVYyIEg5j1EWo5j0EVjoYgjQIKCNURGx0EvHI8x6WH15iQIQ8xo2OL8CyimP7tzjAH6wXuSsgPwD6zbXZ3RnlS/0GZEnYYAggg+okSzShpr2dO3OkSAJkSRIMhCJE4ztyAIgmFuRIAEjmCRxGGCfEjAV3TYMqW44cciaBXcW6SuUUxlIT0lEosuTei+iJpECZNqsh7kOiPUTPy87qiA/LyW16HQlfbqWJdjcz8zG6bjG/KtWtB435P6T551D4jyOrdVpsWkrgVPxWTyT7mNswcnOv+ZlWPcw8Fz4/aW6OlCsflH7iDt2G6pHosBaMzHV6m3sb1LaYiL4AH7Tz1Fd+DZ8yliB6qPE3sPqdV6KLCFf1gSwVrS4lQXxGrIGiNg7EIS1MrxoISZEmOQ6TucJwXZg0IYMIRTulI3Y4UDzszKz/iLGxEOmH/cTFc0hlByeI2WsrqXvsYKo9zMTqXxLjYdbdrqD6HfJ/SeP6r8U35BK0cj3PgfpPPWtZk2m20lm+5lTm5G+jgyl5kavVviTKziyVMUrbyfUzESvZ3rk+THLUSfGo+vHP6wxidSFca1kUBVRvxoftLlVGvMZVQRriXaqefE0QgCVh1GN9ppUUfUOINCaHiXaV5lyiUObLGKvY82ahsCZtKfWDNOnjQiSQNLKLzLCiJTzLC+JU4jBCSBIk74i4QkeY0EaidyQ3PMmCseCDGqYgEekYnMZIRosK0ejSsscsdFMkPEkrBEMCWxRU/AiysHnUURqXGXYnn+v9exeiUE2EWXkfRSDyfv9hONz+E3LYIKni1jeqdVxelYhvyX/AO1B5Y/afMeq9YyOr5ZuuYhRxXX6IJX6j1PJ6rltk5T9zeFUeEHsJU7ps4XAjUu0l5Mtt7l4QRfmAWgkwdzpYZtCLQDucTxB3IOjjI3IMHcZDILc7fECdIMg9yNwdyNwBJM6CT95G/vIQImDIJ36zh4hQSZE6RuTSHGQRJnE8SaQGdO3Og0J6ZW+8MN95WDQw04uF5YDQgYgPDDQYEeDJ3FBoQMBBk6QDJkIEBJ0JAMnchCQJIE5TCBkIcBC0ZwhyAB7YYE4QwISHAQwJIEIDmHQhIOY5RzAXW4YOoGFDRwIakxQMMRGMhoPEIGKB5hiJ+wjh4lDNYtxNALpNzOyOSZ1YV5WXV+zNXKycRz8puP6T4Mv43W6bCEyAan99bX+8pXJtjxKN1W5n7Siy2fHrs9nrlIdQykEH1B3OnjasrJwmDUXMn28g/tNPE+JiD25dII/qr/2jxtX7MFvDlHzHyekSvjZkPWfSJxOpYmZxRerP6oToiPsYj3EvTTRklBx9iDsHmRJZifMGArInSTOkIQZB8Tj5nekAMIkEbkzoGwISyys9AbjXEvEQO2I0PpQ/CKPAAkjHAlwrK+XlYvTsZsjNtSuof1Hz+kGESb9CbUrqqayxgtagliT4E+W9f8Aii+7qRHTrWqqrOgy8d3/APKWPib4ru6y5xsZTVhg+B5f9Z5c18+IUl+zq8XivNkey6P/AMRcjGC159fev/3E8/uJ7bpvxr0rqA+m5AR52e0/2M+LGr7RbV8eIei/RfZw4TP0GvWcBhtbd/pzC/xnAHm0/wBp+fMSzJGUi1XWIN89rET0CWXHzbYfuWMrnGS9Mp/wEfWrfiTBq32sD+pmNl/GajaVso/7RueFVSRySf1hhJXkn7LFwIL2bGb8SZWQfoOv+7mY7vZc5exy5PuZPZJCcxlE0wphD0iFXcctUKuuWa6vtGUSzcF11HfiWq6ftG11SylUvhEpnICunxxLldepCVywqy9IpbJRdS3X6RCrHr6RhdLtRmhUfEzamEuVPuK0FGlUQRHiVKW8S2spkh9DnHgThxBJ4MTCEFuZ3dEPYAfMhbNnzDhC6jHUatnMqpaNeYatswYDC+h2NxynmVq20I9THRTNFpYwRSHiNHpL4mdkz538f9ENeQvU6Py28XLvnY8GfRRMv4ix1yOjZClO4hdy9LUVTWrD4kTxxB2feFZsWMCNaPiBK2jE/Z25BMgwSZCEkwdyCYO+ZMHQRME+Z25BMOjI7c7cGdIOiSZG5EgkSaE4mRuRudBpDtztyJMgSdmdsyNyNmDSYFuduBOh0IR1O1BhCQBtBvvCDfeVw8IPOV1LtLIb7xgaVg8MPI4g7FkN94Yb7yr3wxZF6k0tB4ffKoeH8yDA6WQ0LulYPDDwYFFhSIYlYPDFkmBLAMMESurxgeDAaOEYIhWjA0hBwhCLDcQu6Qg4faFuJV9Qg8gUPBhA/eI7pIeVsbSyDG1jucCVA+5exayR3HxLqK3OaQUxz8CZ9q73NF+RKNngzszj4wugZ9g5Mp2rNC0AAylZME6/JemUrE2PEqsnPiXrPEqsOZS4FikJQfVscH3Es1dUz8Y6ryGKj0fkRIGjJ7O4xfKI4xkvKNOr4ldSBfi8e9bf7y9V8QdOtcIbjWx/rXQ/vPPmjcRbjAjkA/rHUmjPPh1yPbpZXbv5diPrz2sDDnz00FDup2rYeqMRCXqXUsb/AJebadejnuEdWP8AZnl8d/8AFnvyJ3pPDf8A1T1av8xpf9a9Q1+NM5fz4lDfcbEZSKXwbEe01O1PHN8cWgf/ANvUn/vMrW/HOdo/Lw6F/XZk0RcK3+j3UVkX1Y1ZsvurqQeWc6nzjJ+K+tZAKi9alP8A9tNH+8xcp8nNfuyrrLm93YmQtjwJP+R7bq3x3h46snTUOVb47yO1VP8A5ngup9SzerXfNzLms14X0H7SflEDUE1yJG+rjwrRSFf6TuwS2a9QSkbDVpUZIl14l1kiLE+kw4DSOmU7udyOF4m5XVxvUp9No1Rv1J2ZrqnEVrSdsFhNCF2zg9bWPWHBdNdy+o3JYhRsw9GK5EahKuzM/I6vi4z9tl1Y+xPMsYmfRkDdVqt+hk+tg7o0qk4l2usaGxK1GmGxNCteBBmA05EAPiWVXQkIkcF4liKpEqsYIAjB4liYgYMNTE70YxTG0mFhDLdL6EoqeZYqbTCQJsY52olwGZtL6AEvIdymQyLAOxFX2rWvMaOFmRmXfxCIhDnvJPmSlo95mWZGjBXM16x0iG3XbzyZdqcGY1FveBL9LjWt8xSGpW33lqtuJnVvrmXayD4MhXJF1GjweJUQ8SyniXwZmmhoMixFsRlYbDDRnQvSaYPwVM+K/FPTP8L61bWBpHPcJinxPoX/ABGwiBVlAfT4M+enxxEkvJjlHGLMEwmgmTCJEEwIRgyDI7c6dOgaDgM7c4+J0AxBg+sknmRAE6dOnSBIJnTp0ATp2pM6AhGpEKD6w6Q7U6TOk0JfDQw2ojukhpi6hZZDiEGlYGEGitE0thpPdKoeEGgwhaDn3hfM+8rAwgYOpNLK2QxZKoaEDA4hTLYsMMWfeVA8LvkwOlsWxi2SkHhh4uA0vLZDFh95RWwe8aLB7wYHS4LD7xgs+8pBx7xgsHvCkDS6HhfMEpCz7w1sHvA0MmXO77yQ33lYWD3hK/3iNB0uIdsBNnGHbSBMTHPdYJuINVr+k38KHnRogsfMoWngy63rKNvgzdM0QKth4Mp2GWrvAlK0+ZkmvJamIcyq7fVDtfXrM3Kza6FL2OAB5JMr66Oi33cx1JBaeVPxb0sW/LOUgbeufH95vYOXVkBXrcMjeCDsGLKtpa0HsjVVROasH0jEGxCK6iqJNKT0D2lZqBs8TSccxBXkxlFE7GXbjgg6ErNj/aargbMSVBPiHqHszLbF+0S2MPabDINeIlkHsIyiHuzKNGvAimpHtNGxRuIdY3UGlB6vtEtXqXbBqV3EmB0rFYJWOI3BIEmE0rskS9exoeTLbCDWha5QBvmTA6XsOjtqA9peWs6hU1fSAB+ssiqFREcjz3W+k35ZTKwbvkZ1Q0H8d49jPJdRy/iTFx2fMcpUW7O7S8n7T6VZV3DU8X8YIfmYFDE9jB7CPuDqaqveMz3PxqPGFXc977Yn1PMv41XUunsmXXRaEH1bA4Ij7cbSaT2mxb8RWDBqpFQ2E7W2ZrhGEvDMc5yi00er6DnJnYldqkEMAZ6Ktdz598DWMbLqd/SrnQ9geZ9GRe1RObfFRl4N9cm46Gg1GekFRCMrGJELeoG5BMOkUQw3MNSYnfMYDDo3Uepj6m+sblVWjkPIk7AcTVrb6gJo1czKobej7TUoO4jJmFlm7a9/aedz7gHJ3N+46qJ+0+a/E3xBTgmxrLVRFOu4n1hjHWBtI0L8xQSAdmBXkBmHpPnNnxnY7F6MG6yscd5OtzZ6V8RVZnDBq3HlTNDpkvZWrYv9n0DEu51via9F3dozymBkNYQADNvpebj5Nl1VV6WWUkLYEO+0nwD9/tKpQxDbp6Gp/vLlNhB5mbS3PmX6/tK8AzQrfxLiHYmckt1MY0WUzRaBhiKEYBNNbKGjI+JsVMvo1qOobXI3Pi+TUaL2rI8GfcOtqT0u3t33AcanxbqD/NynYjR3oiPJGaaKBkN4hEQWHEVFYB8QTCPiCYWFETp06KMiDOnToAgHzOk6kRdCQZ0mdqTRiAJM6dAA7cgzp0hDp06dIE6dOkEyBHhoW4kGGDM4BoaGDEg8wtwYTRu4QaJBk7MXCaODH3hB4nZk93vJ1Jo4PGB+JWDQu7iTqTSx3wu+Vw8nvg6h0sd/3hd/3lXuMkMYOoNLYshiyUwxjA0XCaXBZGLZKStHK8mE0tq8MNKyvDDcwNBTLStGK0rK0arbiYMma3TR32jc3WPGh4mH0nm0TabidXhrIFsUKYynZ4MtMeZWcfSZdJF6eFK8cCULxwZo2jepStHHMzyiWoyMk6G/aeB+LL7jjoO4qtrEKB6gcEn9+J9EyavpJM8X8WUdp6cxUdjY7f3+Y2//ABDXHyCx5E8v0Lp3TMrJarMBLsv0DegTLeLlN8LddqpSwnp2Q4Uqx32H3EynpNF621MVKncp9XyL8xQ7nZX2Hibk65V9WvJg/OE90++YjiylWB3Ht4mP8LXNf8PYVrHbPUpP9psOdCciax4dOL1aJfzEPHOw3K9jcwJhwS0X6w2OhADSaOkcREsOY4xTHmOhWVbBtjK7jgx7nkxD+DGIVLPMr2GPsMp3WAHUOBI3zI43EHIRTy2oIyay2g43J1YuosGWunUfMdn9uJR+aD4M9L0rF7cRCfXmFRJpYoo0vMd8mPVAIXbLFECg2U2pGp5D4zwGbHxM0DYpZqn16BuQ39+J7vsi7sRL62rsUMjDtZT4IjxWEnTJrD5ArcSteVUknwJ9Ay/+H9NthOHmPjof5GXuA/QxOL/wzx/xQszs976wd/LRe0N+plu4ZpUtFb4BwbGrsymXSWN3L+k98FnY+JTiVLVSgRFGgAPAjSJmtW+SytYsA8QC+pDvriIZnJ4WZJTjH2a4VNj++d3RA+Z/TDQN6iVO+KL48djNnuEcDG1dNyra/mJXxrcQ4sqbtsrZD9xFhyFJ4xnSv0MUxyGVQ3MdWdzQpJ+hJVNGliN7zbxxx+0wMYHunoMXlR+kbTNNFbr2R+F6TbYDo60DPhDX0da61k5GSwanGbspQnhz6sR6z7Z8ZKw6LxvWyf8AKfmlEdqUtUkbJB177mziJJ6zDyNaxHtMzKxq8Qhu32AAE0egdS+GF+H8zHzaCepWN/BsHp9p8/Hdr6mJ/WQWZWBXewfSbbJqT8GOMWj1tHw317OvDYvXbK8G0kjTt3KPUaHtPpXw10rH6D09cPF2w2WexvzWMfLNMj4OpZ/hqi1+We6wLr1HH/nc9jjUHt1rn1mSz+jbX6L2PzNCokESnRUynkcTQrq2NzG0XaWq5Zr4MrVrrW5ZQcyIrkWVMYDErGiXwM7EdSXv6fcPP0z4h1IEZ1h8gk6M+6ZGjjuD41PiXXaxX1fIUDjvOpdL0ZpmWYJ8QjIMrKgCOIBjD4gahIBqdC1Ig0Y4jiDDgmK2MgTBhziIoQJ0kyJAnTp06QhE6dIMhCZ0idIEmRrmFIgCTud3GROlIgQMMGKhCQmjRJ3ABk7kJowNJ3F7kd0hB24W+IgGMB4gIHuTA3CBkwgW4QYQNyYMIGGhhovUkGDADg0YrRAjVMGELCtGqZXWOUxWFD1MYp1EqYxTEwdG50fmwGbj+Zh9GH1CbTGdTjf+2aKxTCVrDpZYYypY3BlkjTGOsq2N5ErvyI1zyYqIkbIUaitdX3DQmB8RdKbqXShVUo/EY7M9PHJU/mT/AEI/SemIBi2QHyIyRLOMfEbHHcyuO1l4IbggzPtPzXXHpHfbawRVXkkmfaeofDHR+qOXy8NWcjXepKn/ACndK+FOidGs+dh4SLd6WOSzD9CfEZPDBOlplromH+A6TjYx811qv9hqXHMZrUrXsQNbmC1+WzTBfoTa+ieZVZ9nzAtuGzvcQb19CZmc0a40SGXP2iRW3coMQWNrADZ+8kP8tu2LG1bg7paRYZuIl2gPZuKZ5pizM4Mh+dxL+DGM0VY3BlsRMwpWnyfaee6z1avDr7zvu/lX3M3cltVsZ4TqNj5PWO4DuXHYcfeaKoqT8lF0+sfAm/H6vmIb7BYlZ5A8CZiZGRQ+1tYEffc3cvquVcGLdqKR4EwiO4lj6zZKMM8GBTm3p6n4ZzrOpZ9eLZ+beyftPq9NYStV9hqfJP8Ah/V3/FlY9BWxn2HWpnmkjbR+XhkQ1gwlaVadqqpJDAsntg904NzHLfrQetTtwSZG+IGUWUxDLACJeweki9mCbEzH6pjUnVzFCPXXEwcucksiiquiO+S95nBZXq6hhWn6MqlvsGEsDJxx/wC6P7TkyjPfJsjFL0MWsk+Jo4XTbcghkdUP/UuxMxepYlR2zd2vaX8L4pwaHCsrkE+deJW0wzjJR2J6XFGfiBVyKKr6f/uU/Sy/qpPP7GXrsOjJTtsrVgfcRHTuu9KzkUU5Vasf5XOjNUIrr3KQQfUGBQl7RxpynGXnwecv+GcZxulmrP8AcSi3QMnHLMhFij28z2JoMX8nmWRssiPHky9HlcSkltEaI8ib+LToc+0PIwQ7CysBbB//ALfrIrtAXnhhwQZ0qre6/wCRXLsTnYKZ+I1LgEen6z4L8TfAnUvhXOvdsW3J6PcxZMmpe75ZPowHI/XxP0DXcuuSI0ZIAIBBB8zoVvDPKLZ+Vj0t2AatgyHwZf6R8K5vWc6vDxa/4jkbsYfSg9WP6T9GX9O6TkN3XdNw3PqTSu4dOPjUIUxseqlD6VoFl/YVV/2efxelVYOJi4WOD+HxKhTWSOW15Y/cnZmnRR2KOOZcNejJUCVzGUMISqW6k0oillmrxM7I1gYSNQaM5QIeoUipsNRGCAsMeZZHwVMXmf8A6Ow/9M+K9ccXdTtsHgmfZ8+z5eHY2vAnxLqThs68jwbGI/vLX6M1nopagkaEKQ3iVlGiyYMkziIdGBMGEYMAyO3OPM6dFYxGp0mRAEEiRqFBMhCJ06dIE6ROnSBIk6nesmRhR0iTIgIRudI1JlIgQk7kCdCgBCFABk7kCFOkbnSECBhCCIUgCdyQYO5IkCMBhiLWMEACZIlijHBT5tnC+g94zvoI18kfrAQrCOWc1SeUY/oZPa662DJgvkYvjcYvM9f8NfDdd1LW5QSym1QVHtGZ3wM4vBwb1NTNyH8oP/Mn1N+ixRZ5Ff1j0Un0P9p7/p3wjhY1IXIAub31NhOl4NdXy1xq+z2Ijrjv9jqLPE9JrNfb3eomnZwZ6K7Dx0r2tKDQ40Jg5K6tM2VR6xwvrKbHUpWtxLdnrqUrTwYJPDdT7KhPJgloT8RJaBM6sPQfdIJiu6d3GOWNDNyQwitwXbSxJGS2CbGvcBKVpLk90EZdLP2s3Y3/AFHgxpX7Tici2bk0XU1RitKxQe0AoPYf2lrs5nFAfImTWaCkWrU670B9iwEz72JcnR+00crDw2DF8apmPqy7My/wa0MflEqh/k3sD9JdEDFF7B6bki30MYyRRWaIWNFMoJjN78GA67Bk1kBtGWPlgzfTJSRgui4mVkUM9ZE+eZvdidYy6nBBZu8fpPrHyNnxMrq/wxidXQfMBrtX8tqDkfb7ibK/Bjti5rEfNLrGcdqjz5Mrsnamvb1nr7fgnPrYiu6mxB43sGMo+CTW3zMu3vA8Io4P6y1yEjxp/wBB/wDDnp7L1KzLcEbq+nf6z6aZ5v4fxhjZj6Gtrrj2np9SmbNNVbr9ivWR6xvbI7JUdOq5YDOBk9skJG0ud0SQdyZISF2SaUWXpIU69w1MnqHTxYpIUGbnZF2VdwIiNJmB3PtqPmHWvhZrmNuG3yrv6d6B/SeQtt6p0/INN12RW4PjvM+1ZWF3qdcETIy+nYeYvyc6kMPRtcj9DE+x1+JLUWZ9vlPGfPsfN61WBZXl3Nx/M2x/nNCr4n6rTr52NVbrzsEb/tN0fCr4yn8FcLq/RHOiP3le3pWQmw+FZx6roiRypn7SNMVZFeJFJfjNlP8AE6fr/tea+D/xVuwP+UmTWB6d4Yf2Mx7elW2DQw7if+2UX+FepX8riFB/U7ARf8fjv9AsnY158n0rA/451hwMqskfdP8Aaemw/wDjB0TMADL9R/pYf+Z8Wo+Cq62U5mUWPqlQ/wDJnpMD4doq1+HxQgP8zDZlc+NU1kDIoRb2ccR9aq/4gdFtI7fm7P8A07j36xj5/bbjqy+/cNbnicDpVdAH08+vE3avo0BwIKeJ1fZseMau34m0Mpj7Ri5DH2mbW8crial7LnBGklhPrLlTAgTHS2W6skDz4lyZTOs1GrBWVX+k8TvxfHpEPd3cySawpUJfsso44EsVOQZmo4Dblyp9zKwTjhoowMaDsiVqiNSwpAMiZkksGiGPMAGEPMsiVModZu+TgMT41PjGYd5Vp1rbHU+pfGeWMfp6Du0SZ8nclmJJ3LGzPb/QuQZMg+IumfASIEM+IG+YBkCZEkyJB8OnanTooSNSJJkSBOkGTOkIBOk6kGBhOg6kzjIgkTp06Ehxkzp0UjYE6cJxlYoQMmCBCkAcIUEQpAkzpwkyEJAhThOkASJMgSQJAhLGoO51HuYAGhG0n+Mn6wMBdymHeEA+lRoSsTH5Q/imV9SAZPcZfwMhRlUC5A6d4DA+25nx1B1Yh9iDCkKj69QaseoJTWET0AhtmAeh3KldnfUje6g/5RZfma0sR0IrwaNeX3HWyJaS7YmMlmjLlNw15lcngepoOe5CJ57PXtuOhoTbV9+szepVkjuEauevAxWMw3PBlawcSzZxuVn8QyZog8Ktq7AldllsjcUyxdNUb2it2zu2O7Z3bGTGfJE9shlj+2A0mlMrm2YXUMRX41Mhc/PwnKrZ8xR4WzmeqtQMTsTHzMFH7iBzM1tMZllPIaeFB/jOrECjOxbKieO4Da/3lzH+Kum5n/IyK2P9PdozLsx0Iaq+sMp8giedz/g1LCbsKztPkAHxMn0Vep+DX9s2tj5PdW9TofyCIo3VWAlWGvvPnj4fxJjU/ITJtav0G+R+8rJjfENb9wyLgfu5MdcOP6kgPkP/AOJ9KIB9REuNH0njacnr6qO/LrB9mTcvVZ/Udasepj7qpEV8dr09GVqftHoDrctYrd21J8TBqtzb9AhRv7TcwcWxFDWHnUtoqlGWlds4SWF5V3GBRBA1DB1OiCmuL9EfJB5ibKZaBGoDkQM1qvChSBRlqdcHibanYExb20VYHkGaGLkCyvY/eKzLyK88l0QtCLRo0ERGzB2aO7RJCiTsSQYQO1nanTt8zj5hTK5TbOkEbhekiQr0rvXuU78RbFII9JpkRbLA0NGTXo80+NkY7fQTqJObanBJnpmpVhyIpsCtvT/KZ5caLeo2R5jSxnm2zrmGhCrx8vJIP5V956Nen1A/lH9o9MZVPAjR4yT8sEuZ/SMfG6RWCGb6m+4mvRhhANa/tLK1gekYo1NKgl6McrHL2dXX2iO1AUw9xmGEsYamMDcRAbmT3RGjo1Wp+yyr6jVs14lQNDVorLvDLi3EeTD+YSZTDRitA2K0i4jcy5Q+jKNZ2JbqPGxKpMomkalJ237SxuUqW51LYOxFTMM15HVniN7tLs+kRXK/VsxcHpz2sQPQS2JRI8J8b9SGRkLSrfSpO540nQlrPyWy8myxjslpUaO2YpvWRIPid4kMdwFYLHiBDPiQfEIyQBkSTIgGInTp0ASPWdOnSBOnTp0hCIJhQWgZCJxnCcYEEidOkwsJ06QZ0AAZI0Z0kSsBM6dC0ZCECFOAhASAIA5hATpMhDpOpENRIQ7UIDiTxJkIcIacOD7GQBCA5gIXskdypaBwwlbUu4wF2I9ZOyviViuuD5kAxWpp9Dx0yOrY1dg2pbkSiBxN74WqB6qGI2VQkH2MePskFrw9tsHgDQHAES7aJhgxLn6jNEn4OjFE93MdW5BEq75ho0zuTZZhrV2b1JvX5lR95Upcgy6n1LEUurA1h5vKQo54lCw6noeo43Baedv4YiXd0OvIomLJkE6iy3MXuM0H3SCYstO7o6khWHuAxkFoJMOivQWG5XtrBHjmWN8wG8wkMnJxQ53wDMqyh6rD2HU9HZXuVXxwTyNyuUVLwX12uPo8/bbkAa0ZWY5Fh1zqejfFRj+WCMRPaUrjJGl8vweeTp1jtth5l6jpYU+JrpirvxLC0qPSaI1xRmne5FTHw1TX08iXwnHiSq6MYJavBR2EMuoJ4jmAimh028e/AC2op3El4hzA2dGNqYi9uIvBy/l5Hy2bQaRceJl5JKOGHBHgxGGzJRPY12x4sE8707qYtARz9Y8zVW7fiKzkTjjw0A4MkMJSFkMWQdipxLgYSe4SoLIQeHshHEs98nuErCyEHjaDCxvciK75wshAN1OAgB9ydwpgGAQtARYaT3feNouDRqEDEd04PDpCxuSDK/zJ3zPvJpCx3SO6I79+snvgbHi2vRYDw1eVQ/3hB/vFZfG2RbDxiNzKfzI6t+N7iMf7JM0qm2NS3S2jo+sy6LdsOZoVH6xKJA9mnUZdUyhSeZcQ7iJlU0WEM8T8edXDNVgVN+U9z6/ynqcvMrxKizuAdb1PknU8ts3qV+QTsO5I/SaIGO54iqfJgkbM47kb4jMxMEwYRPMEmDQIg+IMkyPWQdIEyIU6QOA6kSZBkCROnTpCHTp06QgJgkwiYJgYSJMiTBpCJM6dIwkGdJnQaEECEBJ7ZIEXBDtQtSJIkASBCnCTAQiSBucBuMCwEYIWH2yQIYWDQAhYQWGFhBZNICBJCwgsILJoRuK/yrQfQ8GWMnHIfuXweZVUTSxnFtXym8gcQaAo9up6P4VqP4m63X5U7d/rMJ6ypII8T03wsAMfIJ/qEur9j1LZo3XYIvJ1Pm/wr8VZvxL/AMQutfKsf/CMegV1IfHcGADfqfqnv73WwMh5B4MyekdF6b0Ci2rp2OKha3fYd7JP/wCGXyaSenSVb01d/eEr8ylbd2c6Yj7DctYuVjGrd3zFHuUM4lvyMIT64aHU0tZfrPMfd1DE6dUtmZk049bN2hrbAgJ9gT6ytjlH+uli9fuRqeQ/4tfD/UOt/ClGR0rvsyun3G75KDZdCumIHqRNfFsjf6ZRZ4R9CdFvrKnRB8TzfU8E1MSFmL/ww+Mm+Ifh9cTNUp1DAVarNjQsUcK368aM91dUmQvI3LWnF4COr2eEs2DqJM3OpdLasllHH2mG4KnmI5GlLUAWglpDRZMikBwGd04tFFoJbiWKQjiN7pBbmJDSe+OpCOIwmARuAXkd8fSZgXYPad2CB8yT8yFMDQQAELiK753zNQ6Lg3cjuiTZBNkOk6jWYe8WzcxRsgm0SDx1EsfMTYYL2yvZfxIXxkxd7DUzMo+ZYuyP7xLYGfkqWqxXYffiJKSXtl8ZSMl72x7u5SRqbeB1+p+2uxwr+xmbZ0Pq1j/XiMq/1bEenw+O0C0EN6xVOL/Ys4t+cPT15KWAEER4sB8GeXXEyMMA49pcD+Vpax+rAH5eSpqf/I/vI1pV1PQB4XzDqUEtV12rAj7RgeL6FaLgshC2UxZCDyJidS2LBCFkp98IWcRtYHEuCzXrCFspfMhCwajJi9S382T837yn8yT3w9hepb+bO+bKneJPeIeweha+bO+ZKvfO75OwVBlsW6k/M3KgfcIGDsh1Uy0LJIslUQwYjmi2NLLa2Rgs9JUUxqmVuwuVBoYz/UJrVfUQZjYw5E2aFPEpc9JKGI0cfzLZurpUs7AADZ3Ka2JRUbHYKBydzxvXviFswtRQdJ4LD1jwjphvkolbq/XrM7KvYN/DJIQfaYJhEcQDNK8HNm22QTAJhGAZCvCD4gw5GpA4RAMMwDIQidududIE4wTJPiRIQicJIE7xIEgwTCMEwaHCDBMIyIEQiTOnGQiInTp0gTiZ25xnRQjdTtQ+37TtRdKgQIQEnUICAhAEnUICTqQhyrGBZCiMAishAAhATtQwOINIQBzDAnAQwIGyHASe3mEFhhYukwBVj6mNbhhACxgWHSYXWrW5O5TNjorqmFfWo0w0Z59HdeFbQ9po9OvGPkBm5Vvpb9JbCeMspajNNmv3Gd+YanOna/HI9D7w0GpfKWrDtprNQhjZV6d36S1jdYpqTstqcftuQygwDWpO9Cce3gQk9JK3VjL6dWxXH0hx/wDGF+PGx2A/qeJmhFXwNSS4Efi8X6JamCMYyNPGNBsZhVWrtySFAJmili9vkTzqZAUjnUf/AIj2jbNub3JBdDfo08u1PlkbGz4nl8yhLGJXg7jsjOawEzOOagbzv9Jglb+Xg01cdpeSvfS9R+oHnxKjCad+b+JQV9vHtKTpqWQl2Fsr6lcxbHUcV5gMkuRQ8EkyO6M+VINUdCPBZaR3QzUfSLdCD4MsQvg4v94Js1BKmQVjIARtgm2AVgNxCieBpsgm2IZjFljCHB7WxTWmV3sIMr25DKDyB+sIVEsWXeeR/eZ+Vldvhv1lZ8i65itFT2t7Iu4VXQOs9SbQRMZT/NY2/wDKRtRWssS/SNP4Xo/HdStuZe6rHA592M9eyccSr0fpdXSOmpi1bYglnsPl2PkzQAnG5FrnN56NtccXkpvXxKttI1NOwStYsoTaLlhj3UjRIGpn3UpaCrKDNyyvgzPyKeT28GaaeRKL8ldtSkvBiBsnpzko3fX/AEmaeLn15VfchOx5B9ILVi6spYPqEybarMHI+bWdDf1D0InUWSWo50l1eM9EthhhzKVVnzEVh4IlhdxcwA/vkh4jmdzAAs98INKveRCFn3kIWQ07cri3Rk/Ok0mFkGTuVxbCFvpDoyiODQgYoGGDuI5lsYhjzGCCq7jVSVOeF8YEAExiodxldJJlqvGJ/wD+Sp3FnXCuqHiWqqdyxXiA8mOLY+MPrYA+24vaUvQJ2RgvLGYuP2kFhoS1k5tGDjmyxh3fyruYmR1ggFKBoe5mLfbbkW91rlteB7TTXRJ+zm38teolzqPWsjN2gJSs+gPmZBjisArNagkcuUnJ6xJEAjUeV4gEQMRiTFmOKwCv2kBgEgxhTUAiQAPpAIh6kSEA1OhGdqQgMjULX2nakICZGoWpEAyIkGERuDAQEyIREjUhCJBhakakIDqdJM6BhIMkCdqTJgSyBJ1C1OlRUDrmHqdqdqEh0MCCBGAQBJAhSBCA3AyHAQgJwEMCKQkCMVZyiMVYoDlWM7ZyiMCxQgBYYWEFjFWEgATmOReJwWMVYUAv4WXpfk2n6fQ+0vjxsHY9xMVRLNNr1ngkj2liZsq5DXhmiXgGwRIuD+QRE23LUNsdCVts2wlGXoebdRLXyhb1KhTrvJ/QSpb1RjxTSxPp3cCK22a4QzyzUbIAMo5PVa6m7V3ZZ/So3KLLlZQ1dZ2r/Sg1HU4SUkBR5hjVKRcrYxFm3JyWPzG7EJ/Kst4+GW4UEL6mXcXpoKh3+ke0vrUFXtUaAmiHFSKrOY/0UExlqX3b3i7K9maRqEU1PMu+lJGaV8pezLNW4Bq+xmm2P7GAcf7yr6xe5nfK37yfky8aNesg08b1x+kPQDkUDTr0glJdZIs1RlEXSia/tFNSZomriAaxqOkDTMauJsrM0nqHMQyQ4SLMt11FOOJo2VA74iKsN8q35aDj1b2kSH0y/l23v2UqWc+NCamN8OJoPmfxG/p9B/vN7FwKsWvtrUD3PvHFNCOolbsfozUxEqULXWqqPYTRopWuse5kEQ1bU5nyFz360beHDfyZJUekAiEzQO4Tmo2i38xTjiMc8xTnY1HwiZWYb3Klyc7l1xoRTDYkwbTHvQq/cPIisioXUN68cTStqBPiU3rKHjwZu4t3X8WZuRWpLULwa2+UuxNBaG9oOIndoATRWk68TouJze2MpfJkGozR+TseIJo+0TqHuZjVGKatvtNV6ftFNQPaDqFTMshpBYiX2xx7RT4/2k6jKaKDXFfSCcwr6S0+LseIlsMexgwdSQk9VdRwm5y9ctX/ANgfuZJwtt4kjB+0qki+DGp8QXD/APbL/eW6OuZNjAfhUO/vAxuktaQFU8z1XSvh1U7WsQcTNYjVGUY+yek05mYVZ6UrQ+x5nssbpFAQErs+sVi0JVWAqgamnQdLqVwj5MPJvbeRPL9f6ZmY9ospRvwx9V9P1nmXJbk8z60O2yso42pGiD6zzPWfhhG3dhIF42yen7Tp1RWHMnOT9s8OUgmsS69PaxUjkHRizXqaEikqMkApLbJB7IGAplDANZl01wTWImAwolIBSXWqBgmnXpADCkVi2U+0uMkWUgBhVKwCvMtlIBr+0hMK3bO7Y/5c7skJggrqdqP7IPZ9pCYJIg6jyv2g9o9ooRUHUeVHtBK/aQmCiDI1GFftBIkABqQRD1I1JoRZE7UPU7UBAJOoWpIWTQliEBIEICVFSI1CAkgQwISAgQgJIEICKwkAQ1E4CGqwEIAjFWcBGKsDYCVWMCzlEYoiMmnKsYFkqIYEBCAsYqzgIwCMkEgLGBZwEYBDhDlWMAkAQ1HMbAoJRK+VV8xdS6ibgumzNVdScfJs47x6Y/4QeohLjBfCiaJrHtAK/aBVJG12tlQVHYAHM08TBC6d+T7ScLGDn5rjgHgTRK8AS+FSwplYK7OZPYPaM7Z2pZgnkSyRZT7SwRBIgwOlZqx7QTX9pa7ZwUesVomsp/L2fEacZhVsj+0t111FxuXCiBCNcReugbPPPUAYlkmrkpWTv1lR609NwZgdKDJFskuMkUywEKTpxK7Vy+6cRDId6A8w4TSouO9z/LTyfJ9hNXHxa8evsQD7xuPjilP+o+THdvMZIDYnt1BYcR5WKs8QgKth7TENbonRkZtvyxv09Yumo3as39PkfecPmxf26djiZ0CFjsdDmMAKrtjzD4rXjzEPZwZkRokS7xZMAvsyO+WIUluRFkSS0jchBbJ3SvZVsS3BIhTA/IHT0+tl1NVahrkShhAfi9e4myqcTtUNyrWnI5CUZ4iuKh7SflD2lrsndktwz6UmqBizT/0zQ7IJrkwOma1PP5Ys4+/5ZplOYPy4MGTMz8N9oLY/sAZqinu9I2vBaz+XUSXgsTMRcMseFl/E6P8ANILL/lN3H6Uo13ATQWhaK+OAJnky6MjPxun0Y4GkGxNCojwDM6/NDW9tZ8eY/GsLGJ9bfsfszZoEu1jQlSgcCXqx4gUMM1j1j644eItBGgczRDwZZHj+u9Bepzk46tYrEkqB4nnHqI2GUgj0n1hdSpf0rDyGL2UIWPrqbF6Kz5h8lnOlUmEuDay9wU6nvz0XGrsJVABH19NoSoqEA39pMIeBo6RdcfykftLf/wBN3MgZVGmrJ5957mrEpqH0qI8ADWtceIjD1PnjfDGScQW1jbg6dZSr6Jl3XikVcz6eFXkADmINFaWF1QBj6xGTqeIy/gu+rAa+uwPao2awPSeVaogkEaI8ifZknhvivpiY2Yt1SgLbyf1gBJYeQNfEWVEvsn2iWSKVlTtEgrLBSAV+0hBBXnxAKyyVgFZCCCsAjmWO2CUkwJX7ZxXiP7ZHbJhCsVkdsslYBXmAhXKyO2PKQShHmKAT2zu2O7PtOFf2gYRPbJ7ZYFJMvYfTfnuNg9v3hUXL0QoCEIIEMCVlWk6hASAOYxRIQgLDCyQIYEBCAsMCSBCUQEOCxirOAhgRWAkCMAggRqiAhyiNAkKIwDmAhIWGBJA4kiMkEkRggAQwI6QQwIQHMgQxGwmj6RxOcTqjJf1m+r+BrqfgQwgrWbHCj1jD4ljCrGi5HPgQqOsv0sJWK0VR6CFqFqRLcEzyR6zp0jcRjIgwSIRMjcXSEagngwzFseYCaSDo7jDkHs0YiQTxF0ALkt5iWGzGmLMD8hFMsU4jmMU8mEK7jiTi1d1hcjx4kW/lMtYwAqGoUQPU7UmcYQMBomyNYyu3ruKwozeoL3VH9JOPqrDpXf8AKJ2awCym+UGUAEcTmc/0jp8PymWLLufMQ1m/WVXu58wfm/ec9I24Wu4SO4e8qG3nzO+b94wuFvuHvO7x7yobePIgG37yYDC4bNHzAa0a8yo14C7JHEZh1WZr7X6ax6+8tqqlN4hJzUFrNbpVOy1xH2WbKLxEY1IrqVB6CXQoAnbqh0jhxbJ95dgNCdoQ9TtRsEF6ndvEZ27hLWT6SeAFf5fPiEtBJ4XcuJjEnzLdeIV53EbHSKSYjAg9k0sehQOV1GKCODG9ygckCUyeliAZQqk+AOdzznUOpNlZBooJCL+ZhC671fSGqnkE648kzOxqmRNNy55P2lNk41rWaqq3IuY9O/so8ky9V2K30Hcpop7dSxUCs50+VOT8Gr6Uj0XTz8yvW+ZoqJhdMvK5aL/VxNxeDL+LNy8M5t8essLCGNHMSsck6ETLJBrGCAJIOpojIqZLLAKxgO52paBMpkkGQXIlp6w3pzEvUJVKLLE0wFsMIncEp2zgZWMMUTF+KqBZ0vuIG1bYm0utTM+IB3dNI+8miSPnr163EPXNW2oCVbK/tFKzPKfaAUl01/aLKfaQUplIBUS4yfaKZIQlYqJBEsFIJSQhX7RIKyx2yO2AhW7ZHZLJWR2QAK/ZO7AfSP7JIWK/ARC0gmOXG37xqJzLdSb41K2wpCKMHbzbxqhVXrUTSoTmOLHXHE18eSwWSPGCGIsCMA1MCkUaEBzGgQFjQI+h0lVjAs5RDCwaE5VhhZIEICDQnARgWcqwwIugOCxirIURgkISojBAURgEKRCRDAggRgjoJIEISIQ8x0EmEIMmHADkbUY3IlYHUaG4mmqeeC+qWPACpZlUeSZqIoRAo9JTxV3aWI4Eus00xX7NLZxMEtIJgkwSYUiSfacDBBkExNGC3IJgbkbgYAiYJMgmQTF0hBaCWkMYBMDZAiYDGcWgMZCEE8xbHmEToRLtCQVa3Es0t/DX9JTubSmDh3Fi6E+PETQ4anduCxi+7XrFtZ942gDZoixpzWD3leyz7wMhVyx8xSPaecyWsx7Cy+PaehtYHfMyMpA25VZWpryaabHD0Zw6kjEd30n7xgy11wwlHIoAY6WZtyFSfImR8Rfo2x5DPQfiV/qX+8E5YA2Suv1nmKKmtyiQx7V8zWqxA+hrf6xVxAvkIvHPrIOiWP2kVW5OQ+q0492ljF6YDonQ+03MPCSvRAEvhxYr2Z7OW/0UsLozOQ2Qxc/6T0eLjJUoVQAB4nU19stKAPE2QgoLwYJ2Sn7YxF1GRamHuM2ITJA3BBjax6waQIKBGLFb2eI1RqK2FDl48S0jfSJVXiS1yqN9wiMdF0OANniYfU+oO3dTVwD5PvByc9mBCeJmZLGyvtDaZuNxXFJax4rXhWx6/wATe1p/IpKr9/vNFVCgRVXZRUtaqCAIwWBvTU4fJt7yOvXHrHBws7RJ+cYgg+QIHc3tM6LcNXp2Qfx1I+89bv6/2niunApkV2v6HxPYd/1ftNfEeTOZzIZMsqY9DKqmNVp1UznyRa9J24sNxCDAy1SKsGAydwAZMujIXA9wWG5wk7jsnoUyxJGpZOothuUtDpgrKfVkDYbb8AS8vEodYbWGQPXiKBs8jbXyf1lZ6po2JK7pE0Rme9US1cvOkUa+YRSka4tq5eKfaKZINIUzXBNctlOPEWyQ6Qrdkjtj+37QSsVkElftB7Y4rB7ZNAK7eZIWM7Z2ojYUcg5lqsaiEGjLKCUTlg8Swkcq7ikEtIh1Ejf1G6aeLCD0hhJXFwEYuQsZGNRHquo1RKoyF3GLkLH1BwtqsYBKgyQJP4qK2g4XAIQErLkjXvDGQNyaTC0ohARC3CNWwGAmDQIQEAGGDCkTA1jBAWMEYIY8QhIHiEBHAdCHmRrmTGQSZ0iTGSITDBixuGo7mAjxWsi9mhQO2oe5hMYI+lQPaCWm/wBI2xXgLugloPdALSlvyWB90je4O5BYe8XQhEyNwC0jugbIMLagFoBaCWkASTBJkEwCZCBE8wWaR37kE8yIhDNxEM0Y54iGPMJBF2yDEYVms1wfVY+w8GZVlrY14uHgcEfaVsdG69uhEG0e8yG61ju3FhH2Mj/EUfw6/sZNI4s1WtG4h7dnzKIyix4acbzrloUDqNscc8ylcwJhPbyeZUttAPkwjRTKmQeTMrKZVDE+kvZV2hweTMixvxGStI5G9tFwtTwvdKoD1dxXRY7noMbGA1xKuDUFUACbVKc6A4gwRyG01TQpTiKqUASzXHRSxqjUcIoGGDHFwaJO4vc4NAAaI0nSD3lcNGFuPMBMGowjQ4lQPqQbD6SYEuPkKq8ESjbcW3owXeIdpFEIt2lcPtyd+OJOS/ZUzRK/8pSPWYebb0jiNvEr7PsywH3D7pXDaEk2cTgS9nXUS7Vf2+sY2Sv2mWbNGLsv0I0WHqan4wIRojzPVLlgPSNaBQEz52lzWXIo9TPYnIU3Km/yKBNfGT+wycqteD01Thl2I9TMvAs2ut8TRVtDc6enIkhxbUJDE92+YxTGTKmiwphb1FK0Z5l8WVNBAzpAky1MU47g6kzorCiJk9XI7dGa54Ewur3KX7B5lciGQ45imWOMA+ZWBlV1iivMs2ERJk0AspA+SW4CywACZcpVQBKbLlEmaZn4Nz4UyGwX9jN5VX7QwinzqZXy2MonmXwX14Mq2Uuh0VnsDQh9JXuwkYeAYVzP7J1PKdkApNnJ6b28rM62lkOmE0V3Rn6FaKpWCF5jisHXMdsCOVZZrHiKQSzWsy2y8lkR9S87ltF4ial8S2o0Jkky1Hy/ZhqDC+gHX+sIEHwZvcZL9GE4CGJK1s7BF5Y+JayPloqU16JQfU49TEzQoQJIE4CTJ6ISDqEGPvAkiAg0OfeNW0j1leEIUQupkNLVd4PniZimPVodwmGqlinwY0GZS2aPBlqm8nyYVMhoLzGCV67AY4GWKQBhHM4DclYYliYAO2cRGhZPZv0jpkFdsZSv1iMFRPmGqdnMsp/kPFeQ2YCL3z5nNF+JumzaiS33g75gluZHcJmfsdIMtBkbEjuijIImQTBLSO6QhJMAnmcTAZtDiFAwljqATv1gliZEmgC3qQTAJkbhAS54iGhsdmLYyEFPM3Jr71M0m5lRxwYvsKZ5/KxQzHQmVkYg0dbU+4M9NfXsyhdQIGi6MjKxuo24/wBOQS1fow8iXT1WllB7xqVrcYAHiULcZBvSyDZpo2dUpJOrBKOR1ihBvv2fYShZR2k6Ep3U9x3rmHSYiM7rthHbQuj/AFGX+iIW0z7JbkmYN9Xb5E9P0Zf4NZ9xIBnqcUADQmpSNTOxQNTSrOpCplxDLCeJUrbiWFbiQRjwYQiVaMhTAM7pwMXO3CmQZuOJ7qx7ytuGjDxuEBJP3kbJgsdGD3ahISSTFMdSS0UzSEKuc/0qvoTzIpcdvaYGcC6AjyJQGTobPE4/Pi+2nW4bXXDRZ9GKa3XrFHIFqDWtyo9/PpOY4M6CZbe77ytZedeZXa8n2nLXZZpghIlkKmM5JeWXun2Bbxe3ITwPczaxL2sOyfqY7MxMXFyLHAVDr9J67pnSlqVXs8+021V9PJivtUjZ6ce2r9Zqq5KDmZ9WkAAGpaQ/TLuxzposK3Oo5WlVY5THiUyiWUaPB2JVRpYVuJfFlEojNyYIPMKWpiYdOnTpGAXkW/JqLn2nlbrTZazE+TNzqdgNfYTMGysrsjwJTNkAY6inMI7Ig/Jew8CJopWZoBOpYehRvuuUH2ErtXX/APc/ylcpBSYBs7TCGSR9otqGYE1ulmvIU8yqzFSQeD7TLNaw+jQ/HkesNepfpMgvzO7pS4A7G9X1EMdHUsrlqx0DPMrYR6x9VrA+ZVKsdM9A2mH6ypfjBx4g4+QSuty2PqEEW4vwEwL8VkY6HEqlCDyJ6S6kOPEzL8Uiz8p1Ntd3ZeStoooJZrGp3yCD6xyVePMSx6Mh1XpLSiKqr1LCiZ5eyxMpFKSCPlVn7FBFvjY9i9r41LKfQoJwf7iVMnrOFibFtw7h/KOTPfWVVpeUYkY/VsfHwMnsxu4F1DAE7CzNA3zNO7rPSL8sZFmNZawGud61+kuVdQ+G+oD5T44xXPAsUdupw7eJ2k3FoJg61OljNxjh5DVd4dfKOPDD3lUsJzpxcZdWAPYnQO4agltjQiaEM2geOYddit+biVzCUSruwl0a8gwi3GhK1Z0YzcZy1EHqeY+tiJXrBY8RwUr5kSeaQt1W6Mu13cTLUyzW/wB4VPAGoj7jlMpVNx5ltG8S+MtFZYURypAr5MsIJYiHLXOtTtQmWUXxByU/9Ox1L6n+SLY+zNYxTMPedY2okt9U12M2IImR3CAW+8HulDZYhoYSO7iL7p24mhQfdILQNyCZAhM0HcFjA7o2isMkQSYBaRvZ8wgDMBmkM/HmLLbhAzieZBPE6RqEADStZxLTaA8xL9p8nUCIijYu5VtTiaDqvoZWsTzC1o6Zl217lK2ob5mtakp219w+4gws0ybaRoyhdVzNixOPEp20sfA4gwJg5lfHHmbvRWBxkHtKeRihvzHRlnopRC9fd+U7hC14PV4x0BNJGEzKGVgNGXkPiBlRdrYR6niVKzqPVoBMLKkQ+4SuGh90IBu5wP3i+7id3Qogzf3kg/eK7pPd95AD/KxTEiCLCPBkse7kRwCyTAJhGKYwMIFp3WRMjJxi3K8GazDiIddyqcIyWMtrm4vUYmr1OhviWEo/ELydP6y9+GU86Jj8bD/iAgTM6IpmtcqZXwulfMtCle47nqcfo6rUAVG47puOqqNgd02FQARXBL0K7py9sz8bASobKjcuqmvEb2cQPERoTdGqI5DriV1bmMBgSIy0pjFPMrq0YGjoraLSNHq0pI595YUmWxZTKJZUxm+JXU6EMNLdKWhoMF2ABO/EgNKuZZ2VEE+YHLwDDJzcg23NrwDKofuOj4nWfmP6wCJmlPyKEagm2J4lS7I2Cq+PePtsJqI3M2w6JlTs30REM8S7wWYxLsSfMrbG0kvptg8+8YtiZH03cWfy2e/2Mrk8yAeYorOdSjlG8iBoy1YnzKVuHn8rRarFbB1AVdkS7VR48yKqtsOJo1VcSqch0iKaR6CXUUADiQi6Go1RqUtjEdg14inpDcyzridriRPBWUDi7kDG16TQ7QZ3YNR+7AVFq0YwIBG9s7t3FbDp89bHzM8/xc1cao+EXliPvDq+G+mnl8y1z+wjSKm+pQQTCQges+h/XF+zIMT4f6SBrutP/wA4Z+HOl2KVU2KfcN4kq4Ey8zq+fiZxUGs0kbRSn/mUXKqqPaSDpYzPhvIooL4t5vRBv5Z/Nr7TB2ZsVfFWWmiKadj15/3mTdd8/Isu+WtfexbtXwP0nneb9TfatgbBBMtYmFlZ9hrxMa3IceVqUnUqlvl9rMhZdj6f6h6ifd+j34OR06q/p6KuM42vanaP/wA9JTxeO7m9eYFHycfCHXzX3/4Zbr22u/7b3LVfwP15qhZ+ERd/ytaA39p9ZstFSs7ByAN8Lv8A0nk2+LLslmOOKkKkjkEsP1B8GX38eiiPabZfXVKx5E8Vk/C/W8Mbt6fay/1V/WB/aZo4ZkYFWU6IPpPoq/EucjDuath7duty6uX0brvbTnYtQtO9F1/0aZIfRa+sJeR7OLZBaz5pW3b4h95Pkz1HXPgyzCQ5HTWfIpH5qzy6j7f1D/OeVOtbB3Gs71fi0Zswch3HIZWQxobUzrW/AYxbeGhQXLAKpP7TVx6SRtzqU+n2B6vylSv+cvGzQ5M73E4MVDtMS1dZdRyp2Hg7j0bRlRLe4eeIz5nsZZJ8eLwtjRY1pertHgmPcCykrvgiZq2j9Y9LvYw/RGX5QF8xfky7uGIPoZXY8y1mqVt36HmUWaLYsXk2QerQiRI7hFluIPfM7LkNJkbi++d3RGMM3OJi+6D3cSEDLQNyO6CTzGRCS0jcFjzI3uNorJJkQS04EmEUL0gO8h2Ai2bcKAQzbi2aczRZaMiEHzAYiSTFO3BhChVq7GxKjroy33e8S6gwYOmUnqGtylkMEGgOZfvbX0iZt+juDBkZuSxbZMq4tpoylf0PBlvIHtKLLFZohHfB63Fv4HPBmvVdvW55TpmR3UhSeRN6m3gQaU2QcXjNutgwjQ2pnVW6A5lsOCN7kK8wshoXdKwce8Z3feEVof3jUgNFd04NIAb3zu6L7hI7hCAb3CEtmiOYjund33k0hYYb5EQ04OQeDCYqw2vn1EIMFHxIABnNxOEVjIYigN9pcpAJ8SkpluoxWh0bGMeBNSp9LydzIoYKvnmW67xrXmUNDmiGBguOIhbgYZtGvBleBRAJEYrGJ75KuDJgxaWwiOVtymGO45GMIrLaeZYVpURuI5WhTEki0phhohWhd0ZspaHd4HJmdk2G0t/lH2W+glZhs7ma21ZiKmVGqizVLhWLKzI7GKULKzoiZ1tZDGbj1ytZjgncitAYb1nZiWQzZsxvaJbG+0f7EyGSUO5AQ+00mx+YPyADF7EQmpG/CW8HXBEFE2ZoMgrwdeCzSuq8ytyGG0pyJerErVDxLKGUyYy8DgNRgigYQaKQbOg9wnd4kAEJxg987ukA2TJ1OBEnYgAfLRmk3gj8u5ppYoAJmDbi31Esn8asf0jTD9pq0hmoQ9rDj1Gp7bh2WdmpmGtP9mzQtNy7XRI8yj1zBV8E3KPqq5/b1mUc5un9Sdjf2gqPoKnR+837cqrP6baisB8xCo9Zo5E4Sqkt9DSZ40NDTbsFHky9/gTVoAmQG17iQaMbGxbq7Gs/G7X5eh9IX12feeYcWvLFBzrlt+XUgHbWNb9zN3ovxv1To+LXihasjHQaVLAQVHsCJ5ZdrvZ49IwHcEb2pdojo+vdM+P+iZ9IGTb+Du/mS7x+zDg/5Sr8VZvw9k9Fzc2vMxmy6aS9dlFyiwsB9I45I3rifKSCYi6v+G/6TS+X9i6zWlsJtPwfWOgdJwev9Bws2jMvqutqVrU7lYq3qNa2OdzTxPhOijbZt1l7Bj29u1XX31zPj3TOpZnTLEvwsh6X7Rsg8H9RPSUf8SPiCtlDWY13ppqv9pK/8aL3rjLpXz9afWEtx0RaaT+UaVVB3qfMvipKk+IcgUVpWCFLBTsdxHJ/WTf8W9e6lQBcyYtW/FQKlv8AzMhiWYljsnyTF518ZpRiUN6Qm5ZpDFuDFVoWMu1qFGhMME90C1FzGbt8mWW+pZQQ6MtJaB5M73Cv2PWQkvIIttx2GwSn2jlzaWH5gP1nBg/AOxAaisnlF/tGnw1N6ma6+W4rGhv4tPCncs1Wb8GVUprXwoEsKR44miih1r2VXWqf6JyAbKj6kTMfjzNUc8TOzKTWw9jzK7mm8LKJ+MKxaRuA24PdMckbEM3I7orvEHvG4rGH7+8jf3ijZB+ZFwODe77yO77xXduD3xkAaW+8gsAIotxBLcRgDO6GraTulfuhO/0QoVnM24stqB3GCzR0gBFtwCdQO+Cz8xiElopm3sTmfmJZ5CHM2vWA1oUfeA7yvY8g6F2sWYncpX+pj3bzKtzQMeJUtlRh9R9pbs54iu3ZlNj8G2iPkZhjsYkTcqYhRMzDo731957jpPw4eoYJL7rGvpb7zLG3rLGauTVFw39mTVZ9I5lyizfEz8rFv6blNj5CkMPB9CI3Gt+sTYnpx5I0O7mGHlE26PmGtu/WEUvBzqcGMqCz7wxZ95ANFnvMjv5ie8e8jv8AvCLhY7pBY+8SLJ3zJNJg7v8AvOFhHgxJeD3GTQqJdDCwexgEFTK6tr1jFv3w0A3UemyZZRgolA3AcCCLjvyYGMos3KskHQP95YW4A+swUvII5l2rK0OZVJD4bAyde8fXlK3k6mQuUuoSZCltRGMom0LA3gzvWZauQdhpYTI1yTFYepfWztPJlpG4Gpmpaj+ssJevjugJ1NBGj1Mo1vvR3LKtF0SUS0jcwci7sAAPmUb8wKCqnkRCX9/DNsyqduLEY7LEvBcFnOyYXzPvKbMRODmYnpn7lzuBglpW+bO+ZuVsnYeTsRbeIBsgs8BOxxGxFOo9pJcwO7ZhQdFsgkLXs+PEbrcC+1UTsU8nzHDpXyW73A9ANARa8QHaB36itE7FtLADzHCwTO+bCF5EHVh7Gl8z7whZ95mjIhfiOYOpNNL5g95HzBM/8RJ+fB1I2aAsEn5kzxfCF8nUBfFn3hB/vKAuhfOk6hPF7Kng6MclhbQY7lUuN+YVbc+Z7957ExFfqeC+Vkq6DgIBB6KjJmNU2wO3Wpr9pevjk+0q469vUHs1ohJwfkdhskYuQ3F6aFlDKT2EMJn5mOuTSUbhvRvaWlu7TsHmRe6mv5rcb4P2Mx8PlK5/VYiim3tLGeVtNtVpx7hpvRh4I95CWsihTzr1mrnULlV6BAsHKN7SrT06y1Cr6Vx6j1k5HGlXL8fRtQpb+ORGdy2VXEg/TU7f2BibcW7HP8RCB/V6SwtLr0nLtZe0W1ipCfUswG/9ZRXGXfGiyEW2a2N0LH/DY7WMx3Unco99Dc0KcPFxh/CoQfcjZkPk1VD8w0OPMhcyqxfpIJ9tzsuzjwj+tA35IyHDkbI4lftUtrc51BYk6naA8TjXT7yFLKFFGtiNV6v6wJSAhgiKrM/RC73KBsMD+kgWEyssckZWT/RGP7mCkqdRmPZZYu9SuxPaRLuDxijc0132xfli4PrDkcxqVnu3uL+fWh+pgJP46gcbJmp8uX7Y2IuIg3G24qZFRrYenB9ommwOAVOxLasPeIrN8jLweXy6HxrCjroj195SL8z2Obh15+OUbhh+VvaeRy8a3EuNdg0fQ+8vjJSNtc9XkWW4i++R3RfcYGjQhnfO7ootBZ4uBHd33kj6jxK3f94SXdh3CRoc5KkDUWXOoNl5sO4ov94QYNLw2YFJUL/eEtuwRuEUIvAZ/pi3bXrFmwH1jomDPmagNZFloprNGEiQxnMUbIDWfeKZ/vCTAnsiHsgu/wB4l3HvIE53lWxtwmb7xLNFky2EdBPMKuvuYTlGzPRfD3w9d1PIUAEV+Wb2EyXTxHRqSitZe+GOgP1G9T2aqXlmM+nVYq49K1oNKo0BI6bg09OxEopAAA5+8tOw1MG+TPbc5y/4POfEPRF6pjbXQtTlT7/afPWS3EyGqtBV140Z9dbW5idY+HsfqiMw0l+uG1NlN2eJGaa0+eG7mEt3PmLz8HI6dktTkLo74YeDK4bnzNyxrUVmiLT7wxYfeZ4f7w/m/eHAF8W/eF8yZ4uhi3frBhMLvzJPzZSFn3hfMHvAMolr5s75h95V7x7ye8e8A6gWvm8Tvmyr3j3k9w94rZbGCLPzZwslbvHvO7ojkWqtP9FwWmNXIOpnhvvGqx1K3NIujQaAyD6GPqyAeG4MzFJ3HKTKnaixcc1BbzsNH15B3p+R7zNrJ8Syikyt3JB/x0aldqk6BG5aoO2Ez8akM69oJaehx8JaUBcbaLG1T9GW9wqXkmvuGvaG9rDgeJLa9IphC9ORdyXLwhLDbE+8jRBjCNQTK3Awtjq7A403mc69p4PESDqNRlZe0/tJ9ekTB79Tu+DZWyn3EEH7yt1Mmhl4PfAYwSYv0h0MvJUkxY5irsjt+lD+ph+vBtG3ZHZwvJlN3JOz5gFxvkxTPv1k6k0Jmiy0EtFs0XCaM+YZHzDFB53eIMBo3vM7vMABiNgGTo+0GB0P5hkiwxej7ThD1Jo4OYwPxKwPMYDCoDIsK8PuldWhd0P1hPId5LACNxmLg2H8m9L9/vMi3qSUUlzy7cIoPP6zWxrBdjpYBrjWvaekhy1Y8RXGWmjj7ZwAZeyMTWI9v86rMpLzSe4TSp6imRUaXPDDR9xDfFTraHth3g0Y/wA/ZPMvdGyqnNtV+mUjlW8GefynNVr19x+niDiZDJkppuWPbz9+J5alOFqOFVqmkbfUOk24jG/Fb5mMx32Hlk/3EPBxnuIAPmaJx7kyhjIHNVfBub195ZzWXpuLW9aAK7dpP3nsVTlfeR6rj/GueNv2Lv6dRj0oMnVrO2u0+kHqPSWzsOqnHWpe1gxDMRvXgCJyco5CLpvqHg7i8bOvpyAWLMp9Jkru410nVns7b+Mj9eejPfGeqx67UYMh0wIlG1MVmPa7IfUg8T2ttqX4bkj6tgAETB6t0ir/AA+7JYdt6jY7BwftMnL+KdUXKL8HFu+MlGLnF+jIQZFY/wDT3rYPYmMXPevQyaSu/wCZfEw2NitsBh+kdTnXVjtb61PkNOJrRydxnpce6m8fRZs+0uJQpPrPM034tjggml/8pr05GTSobYtT3lsLYp5ImmuuMkelKL4EpYufVeexgUf2aXg3E219WtRNOs7FQk8cSgMuz5YRddo8GMvV322/piEqZvyiY77vy/EhGyx2TuNQGSKXHlTH1V8jYmbs2/IDTwj21y4tm5QqbtEelv3myFySwsRdVjvzFZeNTm1FLRz6N6iALNDzBfICLtm1LI3f0WR9+DEPQ7vxQpDAhvDeks2fCl4TaWgt7S+1qvo93IOwR6TQxuoh10w+of5zdVap+Gb6rE/DPFP0rIGd+DdSthHBhWdDzKbAroSDx3AT2GX8uzNxMhdd6N2n3I1NJNMNzR1L/R80zOnZGHWLHRuw+pGpnG2e1+LOp0VUjG7g1pH5Z4IvqK0CWex3zZxslY2wTb95MELDWwVs025WayB8wyELtzcAjwZX+adwUtDAoT5iLGKNoxkAc9p3FvYYhrYtrNjxGIOa2La3iIayKZ/vARLRrWxLOYBbZnA7glJIujW2QW3OClpp9M6B1Dq7f+koZl3y54UfvPoPRPgjF6d22ZerrvY/lEzTtQ8pQqX5Hl/h74UuzSmRkL2U79fUT6LhUU4NIqoTtUf5x7VqgCooVR4AEDxMVmyZS+Z28FpbdDzOawH1lXv9JO5UosH3RGF4HfBLRTNHSwqnyF+hfUen4nVaDXk17Pow8ieJ6p8HZmIDbhsL6hz2/wAwnue+SH95orvlAz/efH3tepiliFGHBBEkW79RPqOf0bp/VEIyKB3f1r+YTxfUvgvMxO6zFYWVD3OjNsLoyXkuhcpPDD+ZJFv3la35lLmuxCrDggwPnfaWOS/RrVb/AGi8LfvC+YPeUFs+0MWxGx1Auiz7wvmD3lMW/pCFsRsujWy4HHvC7x7yoLRDFg95VKRfGrS0CDDUyqLh7wvxCKNlhKJzZphBFxY1fEzW6jj177nUa+8S3xBQuwilj9pnl2fo0RikbyDmWEAGtkCeUPXMu8apQp95bxMLP6i47rHP+Uonq/ky5JYehfPxaf8A3AzD0Al3p/4vqRAopKIf52EZ0f4VrqKtcO5vOzPcdPw66kAUeJjsu14jDyuVGtYir0/pS4VXzHJZ9eTGvYNzTzF1jce8xrTpiJu43iPk8zybpWS1nNZFmyLd9RJs5PM090Y2x7Pv1gkyubR7iSLQfaDsmIO3OLRQsHrO7vvL4Q0sjBy9FpLdjTGQ6eo8SuGjariG0w2s0xp0vjxmwd7PiW68F3UE8bhUis8gAS6lqhdbEujxY/st/wARopt0osP+br9pVfolmvptUn7zXNw1wRMzL6pra067h6yxcOuX6LI8Ts8MzI6dlU73X3D3WUWDL+YFf1l23qOS501h49hEnLdvznu+xEqn8bF/xL/9M1eGULMgKSqcn3gLmFTqxQQfaW3FLnmr+0Rbh1W/8tvlt6A8iYrfjrl5j5KJ/G2pavJParndbAj29oq/LXHQLVpnPkn0lW03YjGtlI2OGHIP6GUrLCTwCT7AbmFwknjRz5RcXjHvlXOdmxufYxfzLP62/vKpuI8hh/3DUaj90nRieR6ZFtZ2tjfuZpY+S1o04AP2mWg3LlAIYalka9ZdXHTQWNEGpSeY4IfaW/S0WOtoESYYrMLsh+sXq0fG1L3H5hPcxn1L4Z+HbcrpK25ofFDfkUr9R++j4E1um9Ioy8HHPUumY4uoK9hFQUtrWjx/5mL8b9T6mbhj0UZdeMOWtqUkMf1HM6tPEVa+yXk28P477JqLZdr6HiUWGnOtFl7E/LWpiAVHrrzHL8L4uRSt+Bk2KxGwtp7lP7+RPNfBnxJbjZxwcjDys3Otftxu7Q7FPklj4G59CoBw3RGZe8hnt7RoA/abI9JxNfI48KW4M+YfEOO2H1CyuxeywAFhvY3POjK7X8nc3/jDup6o4NptrYA1ufb2P33PE5dpW3YJAnnJUpXPP7PHXxUL31Pq/Tus15/T6ruTodrnzph6H7+ss25L2Y7VHEsysZh4QgMp+2/M+Y/DvWsnBz9VMPl2D+JW35X1437H7z1Od8athPXXXgj6uebeB/lO7VzqpQ6WHpuJ8nX0Ss8NF2+y7Hr3Ri5Nzb4RlCEfqSdSvjD4nz7lTHxsfp1THTXWH5zr9wBxubXSfiDA6hTj3W2JSthIsDMP4eveIf4lF3UcnEpVKsdH7abFfm1fc+0rXF4lMvs7HTt+VqaSUjcxcMYeKlL3XZFij6rbm27H3P8At6TM6nnF7Pw9SO4U/UwU6J9oqzJsSpX7yCXA4PpNBb1b8pmblfJ1chOqDxf2cblfJdl0h6MJ1dvzYrkf9kq2UYpYCygoT9tT1fzQByTF5lNeRht3KCdbB9RORbQorYy05fZS8nlP8KxbuKrCDLeL0nNx7AabQK9gEn8uz7+0zns+Xb2BS7b0APWe1+GMc0pkWi/WQwCtSG2Co/m/XmLwq1dcq5+NH46U5qLJuxMHp2GMnqFip263rnZ9h7wMXKp6hjfPx8ZvlFioLHTD7kR/xCmHZ0q6zKwrsoVbdVxx/E37r95534R6rmZgFf8AhGRiY6Ke63JOu79BrZnobapUWRhVBOL9npKuJR08ryejGC1ilQND3ldKjQxB8zRuyh2FFJVj4lBzpSSST95g+W4dVUe8PDOZy6q4LUQX52TCA9dSg9rP9uZr1fWi+vE4PFTvl1RzoNTYrZAkByDLl2M1QHeut+J2Ni/PsAQbadP/AE6zcYzSzUxSvxODbuq7tFe4b3IvJxre29CmzoN5BkgKw87B9REVLpnkhqZLumjYyOnY1ibRflv7rMl6rMZj/OB6r5/tL/TuoIKjTlNp047j/MJGRk49j6pPcwPJUen6zrQUW+yOk6Y2f9ianDVlu7n38GUuq/FNHS6TUji3K1pVXwv3MuXMpCsBojyZ4/4iptrusvTEoaltEuy7Pd6yXXwg8bJGyUJKufkxMrPfKua66zbsdnmINm+dyvkWVPSyvjqtnlXr4/uPWZjW21+G3+soXINn0N+TY7x7wTZz5mM3ULU8gQF6pYXC9g2eBHVsWB0SRss/3gGwe8pZFmbioHvwbxWfDqNg/vKR6xXztLB+0ZTT9CKCbxGwbfYyfm/NXtJ5HiYf+MUH0f8A/hkHq9OwB3/fiNo31Hpuk015d+RTYNn5TFT7ETKss7WKk8g6M1ujWKlCZqkfxOB9h955zq99mFn3UlRsNsE+omaF3axxMVVqlc6/6LBfZ8wS3vMVupXngEL+gnU32WOAzkgy9ya8nVrqizZDoDyZrdMbFWxWsrDaP83MyaemW3Ul6P4jAbKjzIquZG1yGHBmG2bmsTOnTTCPs+0dB6tT8haCqqn8vaNTeKhx3Ie4T4x0vrL0MoLGfQujddXIRVL/AFfr5nPVk4Pyc/nfHqX5RPQWcCU3fXrLhdbxwQHPj7zNyN1sUbhvaaoWqaPO21zreMMWA+s42a9ZUDaPmc1ksKOzLBs2PMDv+8rG4iCLRvkxH4F1std/3nB9+sQLl94jP6hT0/CfJtbSjwPVj7QpoeuEpyUY+yzl9Qo6fjm65wB4A35M8V1j4pyc8mpWCU/0CYfU+r5HU8lrbnPb/KvoolOgCxyzkhF8xXrPUcPgwpipS9jMiwv9TTNsuZT9Jj8i5mPaOFHiU2B3zLYPDbPJfo78XaDJ/G2g+kAKDO+WJarGVfXEcOosPKQ16kT/AO3K3yvtGV0dxEDsweMDTxrGvdVC+fae46Z8H05daWXW2juHIEyPhboxuuW1l2qme56x1nF+F+kfibE77m+mmkHRdv8Ab3nLv5Fk59KxLp9Fi9mf1TpHwr8LYAyup9zOR/Cp7u57D7Af+Z8y631t+sWhcbCowcRSeyuofUf+5vWKz8zN611F8/qFptvfwD4QewHoIVOG7n8s3QSrjj8sSquSfaT8lGvEY+oM0MXpzO2tD+01MbpX0gkTRSpKtKg59ZTZc/0bIsPpPREYqWG57XA6fXSg/hgSn0PAZqkZ97PieqTECVjicu6cpSMvI5HX8UJo0CBNbEG9zBW/tyCn3m9hfllaRzOSnmss5KF8fQmNfi2Fz4G/ebuQ3ZiOw9BuYv8AjCbC2KCs7fGjFrJHLmZ92Nau/pJ/SUW3vnYnojlYzKGQ+fTcr2pi3qS2gfcTTPidvTM7RgMOYS8Q7ECuQDsQD4mVVuMsAl5J7p3doQCYt7NTpVLwdPjVplgWD3hi0e8znu1IXIO+ZridiFCw1UuO/Jlqu3jzMiq9T6y7U+yOZZ2DKtYWrs5cdRsFiT4+0SuXiIe5Khv9JQusttZi1NvnklZX7gG0Dz7ToVRi17MkkzbHUaz/AO2n/wDDAtrwsvkj5dh/nX/zMpbTvkQxZLHUhE2heZiXYp+oAofDr4MplypmsuV9BrcdyHyDKmTihB3oe6s+D7StxaNVfI/TE0Yz9VYYSv2kkMG/p15/ynsMPCxOmUCnHrCj1Y8lj9zPI9Oc4/UanB0CdH956l792Eb4mWdMe24Zb64yn2wvMK7V7bESxT6OuxMnM+FulZRLV1NjOf5qTxv9DxL1dvEb80aiSojL2ih1Rf6PIZfwrmYf10kZFXug0w/Uf7SvRiOG5U/pqe4FxEXZjY+Tssva5/mHEqlxEvKBGtRZ5ymgjyJbWoa8S5Zh2U8nTL7iK0TwoJiOpr2XrqL+UvsItkA9BCe4qdECIa/ZlU0kZ73DPBZXLFmL/wDqlqbt/wCYBvtI88GZllGTcD8/4jrFW/8A2aAGP7k8GeX6wo6ngXY7EgOuvOpjYPxKa8SvEy8U2ZFQ+Uzq/bvXg6lVHyfeHleUZ6flYpefB73HXpnQRZZ04M2TZ+bLyWLu59t+g+wnn/iPr/U07sPda03oGe1NlrOfBJ8AH0E8zk9TuZ63LNqs7RSeAf0le/q1mZbvIffoONAfaCznOyGQWHP5Xyv2JqPs0uo5x6x0xy6j8TWe8kDz7n954zLclx9pu0XHFywW/I3B/SZvWMQ42SwA+k8g/aZYSbl+RyXNykmyv0uzXUKh7nU0uvsK+oY6eP4e5jYTdudS3/WP9ZvfEtHzOo4mv5kIH9480lYv+jWn4C6LTY5suY9tZ+lfv95o3L9G0P1KdgiEKxRRVjpwAo4hVr9YU+p1OfZa5S39C6ek6TeM7DVnGyo5/WWB3VP6iZHSktxbrKuQpOzNh7AyE+SOZy7vEvxHUtLFeQCy93oYHUc/5VFgGuQQNTGvzPlAMW0CwErZmUbrlo3s+8vqnZn/AABzzwO6VUNvk2Al24Tfp7ma9L/LcXqxDKeOdTPqdK6wo8KIFuT36AOgPEr7T79kwxm4+UbN3XshaX+hHKgnZ3zO6T1u7NcfMRFVl7jqYrMGps5/kb/ST0ZXC9/8gqUfuZ1afkORmykbI/IXpZ2PTiz59zMpB7eIq6w6Zd8+IjAsCGwkeTBR/nZ6j07pRzuZK2rGyu29zWSZbyCuHgCsgFmIZjNj4cRbW+vkqPBmRfjtl5K16JUto/pPVJ0uukpbSxrsA9PB+xmr4DiTcvufoyqTZPV0+ZRx/LzM7o1oOUSf5RqaVvc6acc60Zl9KqCZGWw12hwPv4nrJ1/mmXQs2uSNfqmDVnYhRlB1z+k8bj3mjIaju70B0GnsrFsykNYcoh8sPP6RD9GxloNdNYVvQ+Zi5/Bd+OHhlKlJPYmJQabcpUtbStx+v2no6sepa9IiqNeAJ5nMxThuC7Acjg+ZfzetVpgscc7s7dAtxozFXtEGrfaO9Ravq7NiOt5NNDimvlh+bXvPMdQyfxQGE3As1ojyDKFuZdUxHzGuyLDsn059o00/h2qyLWBtRBsA+DOFdZ9lzn+jn18pWcjX6R5/LxWpuelvKnUo2Y+/SbLLZl5LHRZnO5q4vTKMcd1wV3/0ls+QoR8nobeZCmGy9nk6eh5eU38Kkke54Eq2dNaq0o66ZeD9p9EBatFHaV3yBrXE8/lVB8pyRvmV08qVjeGfi898iTjhpdIBo6fRjue+sp4bmBmfCnSMx/mCpqbD5NZ4/tLC1kY1ZA/llwWd1QccEfmEyz5FkW8Z5yzk21XSx/s8L1n4OfCpbIof5tC+fcTzowyjgdvk+TPsARcnByKj/Ms8Y3ThZcn060wm/jcmcq9kd/g8t3UuU/aLFuCOn9Mx6VIOk7if1lD4h6UMzEozAPrChW1/lNnLb5+O1A/5lfge4h6FnTLqnHIQGHizbk2eep5Djze6/bPmOR06/H5ILL7j0gUqQwIM9hfXVXSXYcCeeyvlm0MihQfbxOj9mo9VdzYcdxUv2bXQ8v5d6DfrzNrr/Sa7cdeoYyBXUbtUeo955bp302I3jmfQKXWzpx3oqUIM512wnqOvC3tHtE8Kh1NTBzrce1WVjxM5gA5143xHVcx5R7IdWv8AZ9N6N1cZmMCzfWvG9zcJGdR2nQuUfST6z5j0XMOJkBmP0HyJ68dWrqdfq7SeRz6THJOtmTk8aNq8BXZB+YVO1K8ERNlz6AUmOymoynF4B7mHPb4MVUtZceePebINuOnmLqXXNoWrW72SYxr2XyINmRWp0BKtmWA29HUVtsqzC6uQdjieL+J+pnM6m1IP8Gj6VA8E+pnphnqBv5Z/efO77icu0FWawux7VBJ8+wjxg2dX4tQU3OX6J5Y6Ec40oRfA8/eV6HZ7SpR0I9GUj/WW+2O49fDO79nbymVjXuAa9DxLgURbjmQHbSqtX2hioe0sU19z6ltcdfURXJhTM9cYHk8TS6Z08ZF6oBsEy1hV105VVttC3VowZqydBgPSfYehZ/Rs2kfgaaKbNc19gVh/vHrqV2x7YU8jlSpWqOmR0nCp6dgd7kIlaksWGta8z5f8RdWfr/WLMvuJxl+jHX2Uev7+Z95yCGQ1tQlqMNMhHBH6TBzPg74c6hot08Y9g/moJT/IcGCHxrok3utmCv5CMp7JHyHD6a1gDFf3M2KcJK/Ins8v4INSg9OyfmL/APbu4I/cTAysW/BbsyqHqf2Pr+h9YLKpryzdDlQm/DKFmkHEPptByMtfXmJvPexAm58P44LgkczJNYjQ5eNPadKxFSsca14mllFKcSx/ZYvDHbWJmfEGcFpGOh5PnUyxjpyJdrLTFpvLZe/6jPX4J2gnhqj22Kd65ntOlv31A73C4I0cyORNLL//AEF+/HYZgU9Px76lcWltj0mz1W0U9KvY+q9v9+J46jItx9fKbRHofE6vClBP8kcWcdNpum0INkuP3iLMOnt+i1gf1g1dTawavGv0mbmZvynHbvtJ8zq22whHsVSgorZDrsS6rZ7e8e6yp3kHTAg/cS5RnGwgq+z7S63ZkLq+sH765EqVUbV2iMq17TMQncr2EiXcrGWm/Vb9ynwPaZ2SXpvCOQQ3I1E7xhPo35NdFqi/It2O4IbmQrG67sUa9yfSao6Jd29wdWX3E3RhJrf0divkRcUUqjyP1mjUdASKOmeT8+tgp03yz3dv6zWXp2Ga9La4b33B1bXgM74rwUA0ixa3XViBh9xAv/gWtWzAkeoijYCPIiweSKJWQf7FtXjJwoP6FoIVWH0Bx+25epvxNaalVb31wY45SAfSy69hOlC/x4MVl3V+jKam70RmH2EWLmUFeR7g8TSszAvgn+8qX5aWjVqB/v4b+8vjZ29oWN5QJ52Jp1dU7iA3DAambb8vn5dhHsGETshSzsvaPXcLgmXK3T1FeapXzD/xBF8kCeMXOsezsqbu/fgD3Mbsty1xZv8AKL9aI5I9U/Wal8fUZXfrVpP0aUTzRvCNrYhrkk8wdUL7N8dStb8zE/rCfqVny+0HQ+wmPTeD5jmtUzPfDV4Esi3HwMOQd+JBulc887H95G9/zD+84809w5TnLT57Z8TWfSop2v8AMSeSPtAqpo6h1QXVHale5vQ7iLfw6kgVrv7CJTKNNoev6CPUTnKCS/BYcJy01MzGA4G+BMq2k8iaVPV8YntyHCljoN/vH5eIO35tY2Pt4MSLlH2BVuXlGZi6yVOPYdOPyMYzqNdmR0wFhu2n6WH2gsvyrFsUfrNCxhWa7mANVw00ZyySZdCB42tuyxT7MP8AWex6tWLczpTH3J/ynmeqYRw721yh5U/aewtpa2zpza4RCx3+glt81+Ml/wAlq9A2b+ds+niWMdQ19Q15aAVOTnKqjj1+wmn0vG+Z1Fv6al5P3Pic/HLwKvY97NMdSBkaMZmY4pIIO9+Zm3kpyDwZklVksYZPCt1RD+HdgfynY/SKxLVs3ktwAABGPeGUqzfmBXn7zMS0p0yuvf1BmB+/JmyutuGA3zpsHMTt4O9yrZnbs0p4AmQbXDfmOh4j8JDfavooP94foUfIdPQ/lQbP1Gsd36mafS6ymMdjQJ0JTtxuyhX2SeAZtVL20qutaWUSg0MiK0Y1OF8+Yvp21yWYj8oJjaW1ZHY1Vf8AFZG/iHyv2mOb8Ea1mx0bMxE2+U6I29juIE2LfiDpdYPdlK32Qdx/yngc1lbt7edSp3dpOvJUgTt8T5idVSrUfQndxbSN3P8A+IOIOofgsLGtsbevm2DSj9B6x2Ln5NLPkaUpYdke8+Zs4Tq1rtwdED9dT6B8OPVkdI7jYWfxpj4M3/5d9/8AF4y6p7Fo3V+JVoB3jFvb69QMj4ruTivHQbGxszLzMYqpKciZ9u2xk9GHE5c/leXGTi5FMm14G5nUMnPt+ddrS+0oZOWzJoHX6wrbAgKb8DxK2IovuaxxtE9PczHPkWWvZsVWS9E4tYqQ3uQbWH0n+kTmHzFYb+pvB+8ZedmVuoo9WJUVJDMfMSPljw/Fpl7Hqqxqwlf1Nr6mPvNGpKaKvxGVydcLMzp+JZi3pbe/zEADag9YzmsJROS3pKbF3niLrLZWPZFrK6ouaneCNISqge0zTWCxY+TLPT8TFPRBdY9oYEs3aB5/eBff05U/gm4svLMzDQE6HFhCOnS+P5VVEX39lrHuVaPl2c68ETkyDW/0716/eYVvV6gdUkt+0uYORffjvYyMnaN8jzM99H5OSOdzZQsuc4embAsWrbrwjDx7TJsQLaSPG9iWesZAxsbFsQ7WxdN9iJjjqYe5aDrXaSD+kPHUuuB43J+lSj/YVrAZYO9N6Ts3qC1DjYa5ANfp5mfk5g+b3AjYkdSobJWqytSSuyAPYzZx1jxmPiyir02Z/WbVbA0Dz3TO6d07K6knZjp3FW0d+kjq1d1NVfzAVDN6zY+Eutp06q9HpDg6bevWdimuO5N4jr8mdd/IXZ4jZxfgm6p60ysyit3G1Qclo7rGHn9J6TcKk+YV0O4eg9ZZy+oYuZ8nI/xS2oaCmpF8n9YvqfVFz8L8LTYaqhwWs539zLv8Wr8nNr/g9B/kVUU5CR4JLslmGqgefczQqe5W01fJ+8tfh1pfSXJZr1VTr/OXMLBN7/NfhQfPvObbOC8I40vl7E8T0qLXkN9Wu0enM9HiU42YtVuUx+aiBeDxHfJ6M6KuRbaWA/Ih1G1N0un6cfp9j+m7D4mmjjxeTmXx+QtnHyaFDU49fZV3aktkEggCUxeq/SK+0a45nG7Z40JffXV1/EolLs9ZF1lhHCDcgYmU2PZkMqqtTBWDNpgT9oVFeVl5VdNSqWZgB/vPdUYmEt7UM1V+SO13RiCRxoHXoJybsh6DGrueMw+l5HU8mrFrZU7jst50vrPoPTukYHS6QmLjoja01hALv+pnh/gbJVPizrFCue2zvZCD50/+xnur72quTX5SeROrwlDrrK7IuLxHkv8AibgY3+BV9SChciu5U2OO9W9D+mtz5M+SEIHYSZ9J/wCInU1y8unpem+XQ3zbNjQZtca+wBM8W9iA9vyl/tKuSq5T1F9PPnVHqYjZZVwH2gPg6ifxZLnakoPWb/8Ah6dQYD5tVTL/AFy10X4eTqnV/wADl5lVGOo2bANl9ei79ZTGEW8Qf9Qt32Y+FajkMpBEttl0Vn6rFH6nU+p4/wDw8+D0q7H6ecgkcvbcxY/2IAmvh/Dfw30/H+RR0bC7D576w5P7nZmj/T5Nbpqj8o8zD4i3VqFOg4J+xm38P3ZnVc5aMJlpsUdws9eJ9Up+F/hpLXsToWEGfyflA/2HpKOH8BdB6f1Vuo4TZOO5J/hLZ9A37CUT4OLUyu35C2SxLDzfw11Dr9Ofn5Of1a3Jpqs+X8m1QVH3HtN3A+LvxlWXc1NbJT47Treot/hjOwx1BqCuTVfYXVVOmGx6zETBrp6e+KiPXadixWGjM87r63kjB28mzj/8Q+jWntevJqb1ATuH+UPI+MPhjqOJbj5NrlCCAGqOwfcfeeFo6a9bMylGC8EScemvNsZLbfka8aE2x5LlHGgRtaeiKrktJNe+3fG/OvSes+Hxqear6c4ye2q6tlHr43NjFzW6cyoy8k63OZfHz4O9TzoTrx+z29mdXi4pd21oTy+RfZl3F+efErdXHVsoIMb8O4OiQbNECO0uN2NcHBC8heRuX/GcKF82pywuolU5ZF+QhSVbT2Kp9iZvdL6lXiD5d+19j7zwPXj1C/OFuP0kXgaK29+u39pbx+odRpQPllTaOAByBOjR8dGUnG2GL+y62KnHJHt/iDqaXY9OPWd9zd7Eew8TB7yZn159jjutBct6y69imhHA0SeZhVdddrhF6cnKlPon5Gh5FyixNSt83UsuGVRv23uV/JNwrSRl+Vh0rSKuK7VXds1a+oMgIbmZV2gVceSItsn5akt59pi4XyEqXkvRyuNyfq8P0HkdS/iu1gIAOh94sVreoNjFXdh2k89sqL9bG+0bP8qn/WWqgP8AmuxB8iGvkxnyfsn6LK79t1+jexunHFBsYp2sPqOt937TH+IOo0KgrpzVoqQadWbtXZ/WaFHUHrxgbH2PdpWy8XpHUVW2/Gpu9y9e9T013+NyuOlXZh6XiciiOSb8GV8J9axP8W/w/Df573tuxkHAUA7JPt4ns816cVCS/wBeuADMDDfB6R1B8TBwcet2oW4OtYAZSSNcc8ER1163JpsahG891YIP+sPHVdFPRS1ic3mVyk3WVrcnvsLsR3GJa0EmIs+mwj1koj2NpFLH2E5srHvs4bs1hFzvyYHzX9GMhtqSGBBB1qCp2dQKx/2T7P8AkP51v9W5WzsuzFUMwDe/PiLz86rBqL2Hn0HqZ5DqXXsu/vCKoUfy+Z0KbLIryy2MmP6j1XOa9rK8hlA5Cr4mNk/FGZkV/Jts2gPgcTSwUJxUsytIbTpAfM8vm45x8y6o+VYiWu+f9j95G7h/GORiUGpcehwTvbbB/wApZX4zyH//AG1Kj7Ezx+uYxWIMV3Wf2K5yX7PdU/FWJYu7EdW9fWWq/iPDOtCwj9p4BHO5dpfgSt8i39Mn3zX7Pej4iqA/gUM/3sOv9JI63Zb5KoPZRPJU2GXqn3rmVS5Fr9sSV83+z0Iz2fw5MYt+/JMx6W58y2tnHmZJTbfkzyfk8U+Tsn6T+8SbC3maNmHonXKxH4B7HC1csToL43EjKJxV/QhMb5w2da+83cDKfExBVapept9pP8n/APKeWyczLwrXoNbUuDohxoiWOiYnU+o54trewIv57XJ7de33l7p1azbRW17PQXqvPYQw1viXsqtaei4ZsPNiFh/eDldMFGLbbQ7GzX5fRozIrfMan5ylErrVFr9gBMNqUGXyhGP5MpNi19SwPkvpXrOw59RNlGUUKSeFGpTOGjKFUlB7CWlqDV9nt4mWyaaS0qbi/RHTsmikWfNJ+Yx0p14E2KrqMfGutBBJPcQDon2nn3VRYNakZl/ysNudd3Al0IpvUV91E9Eb68hAQeHHAJmXk/w+6tv2mdj5B+RXsn6TuXOp5VSUhnOnP5ZTKPZlc7EzHvsPPMrhmd2C6O+QB95D31uCDv8AWUhjAklLN8+s11xxeRFZEY5dbvlv+bfI9pvYNRQLb2EVjQ3riZGN0zJuruymYlaQOwEf8w78D9BPZ4uQuLhjHyatVEfm14jSrcv4mmuDmtRatF1mP8tFALFBpvbYmymHf26IXeveYObnfJorbvG0T6X/AKgCCDNFev2so+lNe4mecop/mR+CymHejFmrPaPJEoYj2WZNugR2twZdXPyspVGM1RI5sQt2nX235lkV4jIbl2DrlV4Lft7zVwPiK+XslLwjo8Hhf5PnfRl2dNyMpmsxiOD/ABE9QfeJbp+Unb3Vk872Jr29VoNL1YGRVTmfzfMGiwh9Kycz5pr6hkUWIV+lkIPMF9PEjb9Ki0/R05/CU5unj+sdAtFv4uo7Fg5Q8aMt/DjXYwupuVk52AR5noc0Pv5fy2uUnkkaAmRlk4lqqR2q3I1NfJ4N3CSuXmJzeV8dPif7i8xNT8WW3VrYPgzPe8Vrajevj7SsL2LbDciV+p5aV0o7cMTrfvOHyH/kT7RRzLJqf5I26jT1PB0yL+IqHJHlxKqKlOMFrBIHJ99zG6Xl2LkNYG+gcGb92nT59Z4YfUJjknB4UqWmedvdoyr1POW1kpUcIRLlllNeLZdsfQOeZ52nI7rHd1BazxLqq92Rame6xlTIxR2jVqgEg+o1MLqeK9WfRk17+WX7WH9J/wBpf6dlWbqe1ezaAalvIXvygo/5Vp3ozPGTrsG3TLppsx6raTZ3U3k9p/o+08zT0/Ju6jbiWd4fu0OdDXvPYCr5lb1jwDxOo7HPeVHzVGifeXwvcdeexWxeL0bF6eFQVrZd6t5miyLj4LllHPkQqgO0ORtj4kZzV14Lm7hYv2Sl7AYnVEFmGqnR+vuA/aYORQBitbXWPnAEAia9+RTZavy3BURVlYNg1wHltc+nsrfs8NZnWLYRaCreDues6FfVlpUbD9KDtMDq3RK+qowRAtyj6GA8n7yfgrp9dFVwzXCZCWaept8D0M7XEhHktdPZfw+M7bUo+zM+OalORirh1vYhUsxC+DMPolmsiys+WTWiPWfXbeqHH6kKKcKyyllA2tI7D/8AKZfUum9KsyPnJjVi4Hfcnv8AtN1tWVycvGf/AJOvzfjvrr7tnmMdGsVV8MCDoweq3NQ9FS8d45mtXi1VZgQWEn1Ht9pQ6ziB+s4oO+1aySP3nKhPwchN9MLHS8EOvzbTpPb3mxc1aUAKqqo/bUzbcurGoa21glaDf6TxvUOuZPWMk1Us1eMDoDfLfrBTRKT7P0CMc8np7urYiXdyWd+vIXmPq62bgK0rY8+Zg9O6O1iqzaVT/cz3fRPhbWrMrVaeg8kzRBTsfWvyaqftseQRUF5KKWGj95y2NZb21B3c+iKWP+U9PlYnQ+j2I+beiEjYVhuaePbRfjpb010NBH1NWuiT7TTVQpPrKS06keDdms8x0uvOTqNPdRl0KT2m0VH6N8bnpOpA9HTL6w7Vd3b9LgfU51pV37cyLG6h2llsZQOeZ5/rAu6zVXj5OS4Spu5dDgn3Mo51CpktHrk6fEhn/DrBsbJy+pP418pWP9RO2/2jcnq2Z1v44qwsXItXDqsUMEPBC8sTr3PEtLmrhfD9fS+koaLO3sN1h3ony36mZPRqLuiWZC0srX2jRu1yB7D/AHmGM5LfJJ3V+yz8bpiZOb+JpyqmvqAqeleSdeu549f+apI3qehtoqZ31X2uxJJ1yTOxum0unZeSrk8EQwmkvLObZJSlqMXIwhZkpcg41zqaeDQ9d6X47BbF8Fl3Lp6emMe1LSQfUyE3Tdrg/pGdyXlCnrOm9dYhasrJ/jezVgA/vNwJ+MTmhk9nU8TwWQ1a1K3YCxPkwuodRzEaqqrLurTXhHIm2v5OKXWYyeHuDVl4x0D81ffwRFN1BfDb3MHE61mjp7KGr1rtLaPcf3jenZpoAewd++SDKp30ymurLf8AMS/Fo9FjZZ7eASp9DIzMTD6iv8evTjw48j95VXq+DaP4ZKOPOxx+8S/xBgVFgznuH9I4M6UZ8eUespaJOxN6jy/V8BenZLoF2r8hteZ53Hoc5PjQJnrur9Ut6iUAxxVRXsjf5mnmKM2psvsZSvPmcW7om/rfgoch9uE6WKa1B551OyBX8xfmeVM1cfHstqZ1OyPA9TM7OxBl7ZPptHofecyPKbl1kBWF+ogUpkVWAE+YGVlbx2cOP1mRhW2ojUXKV7TrU50ak6P1Y7+QfSO5uFinF4CV8oS1DL865kxqktKtZeoYA8ETVvxkb6vQeZg5eIfm4lmOpZRZsj2m0th+T2HzNV/yl3T+WmifyFjj7EkG2wKo0BD6kTTgKy/ytG46dqEnyfE7PqN/T7kHkLsfrMFPIlGal+2YarZRs+xlRMyoIO/u7teI/D6pX/8Ap7ySrflY/wAs85g5ZO0s5sXwDL/ykuXuq/N51O2+XC1fXZE7kvkauTH67UbVoUrruGgd7lbIw3OQisdprfcPUQKsTKyawKx9Q40fWamNgZP4euu907h4+rnXtOfL425TzPBzv9OucsS8GbkfxHVF/YRzVfMv7AdIg+qNtx/lX2OqN9HA2PJiVLpWXOvq869ZXyvj+RSuzXj/AIFs4VtK2SOy8gECpBoeBMLK6uT3Y4BRFOj95fd/r88+88t1CxbOqOinx5lXFg34MLsem6erfJu6X1DZ7R3Ytx+xOxPRhgw2DsTxKDu6ZdSRsbD8zT6L1V1NWJbt9nQcnx+s6tN6pjjLq+Ul4Zo5LBb2BOpo9NNQx9q4L759xBvxq7QSVXv9zMgB8XK+hSjDyPQ/pOdPmObbRJWb6NzJoGQpZdd6/wCcQmIKqnus/M/CD2g05BJDHzGXXG0Aca+0zSvlJ7/Qvc8l1zp2V+EyOpW2qa6PKHzret/3nncPHFKHKywSWO1r9/1nueq31rT8iwdwfgieRzen5Cs1yH51f28qP0npeFZOypSkbqrG4+TPuy7LckWto6IIX0En4jwLLM+qzGqax7QB2ouyT5EUaHsJVRsn0nu6MNsdEZjqy2lK290UD6ufv4/aWXciFK2Q87FBaz5dndPyunOiZdXy3de4DuB43r0lWbfxHkWW51tVyjuS09n2TWgJigEy+MlKKaGjLstDSXaT4lNBrzLdQ8RGyM0KjsS9SdLKVA+mXavyyiTKmX6WGpZB3KdPgS5WOJnkKzJDK/5Tv7eolc1G3MrqTQYsAN+I3HwEyMoWsXXsOyVOt/aX1pC5HzAoB0RM7lGPo5agky4tdfaardZKBtj5ihh+2xwJcNidgVV7QPA9B+0ppxJewD1mT7LPWmhWPCz3A+ZXdgz7kVk3N2j1lW28VZb1HkA8GNGLl7KLbGXANjcBn7V2ToSu9ra2plS/L/lPmT6XoITSNP8ADK7d4OwfSVOp4lhqrKfUu+ftLGLf/wCmTnyJGXfusbPA8wwlKDxAn5KNdbikL6+YjOS3LuU9wCqoHM78apb6W1FHKG/MuipJ6UtoH8CwU7sUftErQiPuyz6R50I/vsu2tfJMRk1dtRXf/dLott42V/srdS69caVxcezsCsD3L6AeAP8AUz6Z0rMp650SjL7FHzB9aj+Rx5H958YddOZ6P4S+I/8ABctsfIJ/BZDfUd/8tv6v950FBKOI7XFsVf4s9T1bBreu3HLtS7D6SOV/tIozKqseqm6+oXqiq69/rHdccE1uCCPQg73PHdVBs+r18bnMlUpycJGW+XWfVHsjlNSwZG8c7BlzA+LcV81cS5zVYfAYDTfvPnfSs9sXJWuxz8h/pZfbfrG/EdBqNeQo8/ST7H0M28Gc+Jb+PpmrhcyfHexPonWOi9N6q7E/OotX81o4AhdJ+GaujWmxcy62xh9DWNsa/SfNcbqmZWhpa+1lsGiGYmeibqdleKifiG7goA5nUlz6N7Tr1ndXzsev5R8ntHyr63CZbdvd+Qo3B+xmZ1Syy61PqPyxygJ8n1ielZf4/pbPnA8flfXmIuya6B8uxn+Q/wCV3XXaffcw8/5KfKh9cfRyef8AJW8uP1xXgUuQFYhjzF5VtORQarF7lP8Al+kjJqZ1LjXzFG2A8EehExbcth4JnDhU91HBblF4zW6a9VWKaUt+Yykk+82sDJDVGt24PifM06hbh5zXIeN/UPeeop6g1uFXk1bI9AJdfxG32/svUXnguda+bjpcoP0sRuUKmKtUx5AYAzZpxsv4k6eHqxirK3aQ41vXqJdp+C8phu69EC/UwXk8S6HEs6pZhtq4d814iWEf8hHsOPaaD3hflu3heJSOHfU4GiT28CVsnKLYmkUltgfvOXyeHbXZ+UcFspsp/msNRAy3H2MlvwuOTtj8xz+UekqLecWj633brhfaZl+Rbbk7rYFu36iT4Mp+qW4VOXg3reqYnT6w9rbP8qepmPl/EH4t9H8g8CIGDXa/zcqw2t7eJdp/C46BxjIP6Rr/ADlsYxS/sCkjMpcXX7UDn0PEvstRTRrdLPPJ4jK8Y5drO9XYD47RqWa1uoOrE7q/RiN6iTml4EflicYBVDEDZ9ZX650irNx1y62arLqG1sQ6PEtXKFs+k/QfqXUTlXMKlrUn6+CItVs65qUHhZGUovYswEfqPWFWhsyxaV4tKcbm/RjpSqIg+ke/rNHFx6MTBWuqtV7uWGvJnJQeTrzL+T8hbd4b8F0r7JrJvTHL19LXK6lZ2kVju0w3K/Rert8S47ZmRRQpViqhF1xNjM6RjZlD1XhnrfXcu9AyMHpGJ0yo14lQRWOzqX1cuuNeS9irD598b3Wf4jV0+sHtCh2A9SfEsfDHw+9l9RyanWsjf1LoH9Z9C/B4/f8AMNFbW65sZQTLGL8tr2rcd5A9/EvfOU49YrASe+jL6H0L/wDqIuyXUpWT2168+09Spoxbi7KyD33x/aZdgNNodNjR1G5nU3pqQmtbEPqfSdL4n5Oqra5/s6vxvMhUusjM+KLs05Vd1XRhn1FdIzHx+oj/AIbHXLkU5GOmFi+Vqr8/eNbr+VXjPcMcAKp7Sx4iE6ndbgC5yUZ69AK3ma+3x9Vn2J6zsy+UqUepq9Z6l8mj5SM6ufXU8v8AOvsIOt/eOXEtvs7ixCnnbGHllcLG2BvRnP5/yFfIsxHE5XJ+1+Aa77FIJHIltcnuYMNbmX8+41C35BZT6iKq6kj3BFH1e0wNJ+mZuxu3O66Zq+PeQgW8AnYI9pUGUbqg29rOW91YJTsufaJ9W+QqSLb9g+nuOx7xQxylpcsSD6RlVbNavd9TA8zSFXoayZTKcYi90wMSrGfHa1rQbQdfLZZTylTJyR2EHXkbluytUOtFG9iJVur2wFg53oMPSVWSTyUQSnnosV6qoNevJ3LFJO0Ueo3KAS5GUc2Kx0PtLKW/ItKkjuSVd3vkob16TlVlbe5Dph6iULV/irsaJPIljIyVVu7e+/R17SrV3ZGa1zghKvT3llTlJ+ASsz0aTV99JaeOycZhnOam5BnqL+qJRTpF27Lofb7zyObZZQ3z98k8zVVuMeL8azfxr3ppps2Rz2nmaVyHKoa9Rp1GyR6zyXSci+57VLhqQNgeoO56TCOSdBUftPsJit403P8AFCx7Tf4oQO3KYbGrV4B9/sYtGNndS3g8S42Oac5XH5SdMPBBlbIT5Oe3GgeRDOqcP5rCWVTi/wAkIHdjXfJZuB4+4ltm5BHgnUN8dL6kckd+9KYdwpxClTEPYeX0fEqmlvgqaG/zJWvkDmNy2rxcQm0/Ww0APWUbup1YOPZkuoL+FHvPKX9efJyS1jlyToKOdTofH8WM33n6RoorUvMvReGOfnCwKSBzseRNrDxq1rNq3BWbwRKmBU1uM1pBXQ5DcQK7BhWE1WMQ3lfM7ULONTepy8o1Uqii5Tb1HosOxaAbXsV2XwfG55PM6/0NOou92TlfND7+Wu/PtLmV1XD6fq/L7yw5KKeBK7fEfw9dar11Jfku2yxr/LOpy6OPz2rIz6o9THmceS7KWHosDrD9Vr+caDVQo4+Z5MqdV6gFNdNYTR/pO9Ty9nx8Tn2Yho1jq2g4HIloMz5LZBYMpG1mfn8mnj8X6K3pzPkfkKfrcIeWWbbu0sd+BPI4xfJ6k7Dks25t5Du+LdrfcVPiU/hnGtrubIuqYIBpWYcEzzvGioxcmeS8tmqK/wCKqLrVgKH9Z5+/NOFcVJIsRv7ET0T4tyZLWVFrPmv31qB+VvUR/VP+H+X1MDMWz5ZddvXrkGaIxU/P6K3BuRp9O6inUem05KHl15/UeYVrJcna/DDwd+Jg4WJl9IGPg4tVl3y99/ga2dzYbbj6l7X9VnHtq6yfX0adA+YaQVc6IkrlFe0p9RZgoH6wHAvqZSeQNCJwFdQ/cDtDxESSXknkp/EVj0upZAVfhbfQH2PtKWMcj8M3zKnTu4RiOD+hnpXaq+s1WorK3lWGwY3SXUNS2uzQHb7am+j5KVMFHC6u7r4PL9LwFycsWEaVT3P+09AbTcpc8b8RlODRj1tXQrD5r7csdkCWmSsgDtAA8D2mflcp8izUhLbe7PC5PwhmdU6tblWW1047tvnlyNa4Es3/AABiPjt+Eyrkv19PzdFd/fjc9kPl+pJ37ekFwE8HYl9nI5kIqS8JBV/jNPMv8KdLw+kMl1fdatemv2d9329uZh19Cpsq767GRh6HkT1HVas3qd6Y2LWRTWd22sdLv2++pbxeiU0oPnWs7+yjQltEuQ49m9bCr0vbPDNiW41grtTt349iI+qpjwAT+k9t1HGxP8Papq1JbhfcH3mTVUlNQRQNAa/Wa/tklkvZbG5SRmVVEAS0ohfL0TDWuK56WaJsxvwn8PQG+f1id65mrmtTlEMTpwNcSh8pQfMxT96ZepVtvOgF4PrFdxPJMuvj12He9NK9+HYq7qZCfudQxxiNFjAs+WLbm/LWhYzzidRXLtdmBUlieZ6TGwr36ZbUQC7qd68fpM/D+GWbnJPy1/pXzNNfSK/IrsrbRSfIsr8ePeV2sLttvJnqbOk4iY/yq07VHnZ2ZgZeAcb60Jav39RIpxbwo+txG03EVKvtIysgrjOffiUDklRqJvyCycn6RGVXnQ6INhHrLmHRZkfYe5icOmu0/NsO19F95qixVA40B4EstlixCyiMXHWhAtfJPlveE2MjjtsYc+kr2ZqV8MwHt7mWundLzesWjsqsqo2Ns413CUqEn5DXX2kkLPwQMyr52LmqpPo6cf3Eu1fBGNRjoTez5C8t3LtG/T1E93j/AA8+DgpWg2qjwDvUoXIyMQQRr3E6dcX1Ws9BXx6nE83Zj/KpGPdWUA4GuVH6TFzul39h7R8xPIZZ7VlDjtbRH3iH6ZW31Vsa29geJXZRr7L2ZLuKt1HyzIR6nIIImutv+LdECOd2UMA36ehnouq9JRwRk0jtPi5B6/cTE6fhWdJ6uqv2vRcpRiPBErlYsx+0ZMcfDKdlKbrbgEERmVqt17ye3YPcI3qeMcW81nlQdqfcekdfWt3QabCdM1naP2Er7bjZD0XSr8XqXy6MewCqoDuBOif2npCqNWKyoKAaAYbnx9XyMK7uqd63Hqp1N/pnxrkYzBOofxq/6l/MP95q48a4fo6fx/Iqh+MkevzcNaqu/HUBk8D01PJ9Vxlas5VCkLvToB+Uz2WJ1DF6hjpdjXK6MPG+R+oiM3Epaq5/lclfrA9RBfxlveI3P4MLF9lR8hzGIsPHme7+Case3Ex1uJB7SUAHnmeT6/hNjX8Ddbcq3uJfWy2j4XxBS7Jaw4ZTojkx4TSUZP8Asx8eca2nJej69h314zHtcvbo6QjRnnaes2J1fWN0jObK3rutY9m55jonxD1LCoyU2t9iVG02W8sCPTcv4/xvndRFQGPXX3EL2qfzMZu5X+LyJKybzP6PTw+V43XX4PePltTQbsjtFp58TxOb1+jp1m2Q2MW2QvoIzq2aufUtONksMmn/AJg3wxnkrO63qDLfzpdfaYeZyYcnK4r8UcX5HnQ5PiCPdYz0dSxxkpsraNg75Ej/AAmmpS9Bbu8uCfMw+k57YN+Fhhd1uD3fb2nrSw8aE83epVPx6ZxWVUxlKhgW/eWq1DEdyAsPH2nAaXQHBkkkEMPSZVZJehR1DWJlPY7D5Z4CgeJofMVhoEcykmthveIu76rQwP0tKpt2S1jIs5VQdO4Kv0zHrVrOqps8AzfqCW4mwfpPnfoZRXBdb1sr0fq5O/SGqWey2KGMpDKg9TLoTwuuY40qHXtH3jPlBFLNBJeRsKPYWcrGU4wtt+XvZ3ogek4hVPB1CFjV2DtAAYctvmd34ri8e6Ldj8/pHS+Popm/9xgVHCtyXxab3NwBGuw9u/uYvEwLsfKtAIZW9fY+szK+jdYycsd3WiKCT9NaaYD7mP8AiLPyMHpL4PSD/H12tbZ6+86cKa4QlXclr9Yde+jiQhmI0rl0CpIOpn2AWV/X+XzK3R+rWZvTlOZR8nIqPa+jsN9xLhoTKxmeqz8raYH0HuJ5m+H1WNI8xZHJvCo5+ZTY1n/I7O3t+8OmhVRFs5VQAo9xG51arhrQP5iANRny1YpzrsX+8nd+9JFYWytdKfOcDtA+lBPNfEOS9hKKO3a749Jp5OQ1gUegHEwL7si+1xaAfmNoaHiPxYN2dpBUjU+GMqrK6cK7D2uh7S2twuo9G3lLfV2paDs6HDiZ3SbytxpqrVU8sR7z0TMcigpyHTxBe5VW7H0TsZ/yVX61TTD86e4lnEqrqY3Jo7488iN7frWxR7bgpQtXzbWbVQ5A+/tF+6aWJi6y5VoHfqY3LvFNQLnt16mVLc1MCoWOu2YfQvrKbCzqwId+w/m2Zpp4+1uch4Ra8s0qM4XUksVtT0YiQ6qtbbPdW/P6THxs+mnIOKBoLxz/ADS8bxUvJ2h8TFZW634A5eSzVqp0+vuUjuBHtMu/565Xz1b+EzHu9xL1TAWougEVC3HoPWYK9Tsr6g+QifMxVqZ2TfDjgf35l0IObFa8FxMgWuOeBzIv6xjVYVhDH6z2j7mVsqquroz5/TnazHuHg/mqJ8gzztSHIw/wzk/Mr24myqtRWCqH9m2cyu2tewk9o0SZU6j2PiEK6k78bg5FYwumY9DHTOPm2k+3oJm0h8pmOOhcDyBNNdcUmx88G98LPjvU6ue29Dr9pv8AVOoX14VS4mNk3AHk0HtP+c8N012o6qh8dxIIM9ULr6+n32m1lUj5aAe5myHLojV9Vkf/ACdT4/5CqiHScTXxsy23GrszsdaSNdqk7b9SfeV7rKcq4jfiUcbJFmMS5P0gAk+8jGAN1jgk/MA0PYDzMPO5cORFVwWJFfP58OR4isNNnWqsWH0GkHt9553KzezMc3t5PEtZubXYoVWPap1ME5+LfndqkM9Y8mc6qlvW14ORJmtetmZaux21KPX1lynpeLi0nJwqVdz+ckbKmZq5nA1yTNHpt91GSLd67R9Q9Ne0scpfxj6Crm/xRGTnstYxq9lv5teplK65OmYv4vL7i7N21VjyWnorMTDKnqVRVatd1m/Q/YTzdzp1rMpzgNY+EbG1+g2CZfRXDt+QFCTlrMzONb2mpn+baebv1PpMnH6e+J1JyRtddwP2mPXn5FOe+T37LuWYH13PfdHpq6x0prC3y2KFdkcE+g3Ojc/rj4NM3vo8fk0KtbWKvJbZabWHdl200qiEgjQPoZc6V0W5c/5OfSVqfddiEeQfBE0ut1v09nxsFVXHoC1qfX7kzJZYpLr7ZW14CVExsdfnOoPro+IGH1TpeZnLh/i/4x/KG4BPsD7zM6h0fqR6bfkW3IvyK1tanf19rHWyPT30Z4bL7qWF9TlXBBUjyDDx+MpvyxIx8n3bBsx8Yt3qNKNj3k5HxnVgjdmNcat6LDXEwcHKbO6ViZVmu66pWOvfXMm+pLFNTg9plz5Lpj1SL8LPV/iDGyLFGPj9lrqGbet69JHSalzbFsyO+qrfDDy59hB6PjYfUMNasupWyOn2EI4Gi6EHQJ9gZl5/U7x8S0sGIxsaxVVV4HPmY5/7j0XqbvUuoYWFkWLj4yIx1weT+sx3+IOxx8yrg8dwg/ECKclLwW+odp19pigPdb8jtLKRwVXepSqYyYsvB6Q5eLlL2OSj+Q6eRJGQab6a7GVjZ9PzF/Kfb955w4XUcazQotsHoyqSNRV2ZdjYrLatqnuDKWUjREn+I/X6Eckz2YyFU6Pmd88P4mLgZoyMUWltnXn7xyWPa5VDpRwzff2/WXcLjKOt+zHfPHhpq/cx7SP1hfMUnliZnd4QaQnX3kHIbxxOlKHaOMoVmejTOSi8FvHgRNvU6aR/U3sJlstlz/VbpP6VHP8AeQ2P3aUDtUTLOcq1kS+qHd+SbMi3JtLseCeB7SArn0MfXiqp2T3GWkCj0EzrX5Z0I4vRQC+4hABZfsUMugBKxpO9ajqWFyZ5w5hJ5hjMGuSJmPaoAO/PgQV2TsnQk+orNZsglQRqNoIcg2+Jl/i6qq9P+2oH41lO1/KYv0v9C4beV1xcVkqUDXgmZmX8Q2FyKxx77mTkZPz7SFBd/YQKaO5y+V/DpXlt+TNMalnkLRoY+ZnZbMyse0eu+I1s102lnDDz95jdW68x+TR09RXjV6J0PJl61Tm9NGVUe10XuYH1HqI0qcxv9lU4lfJVWJesgD+mZzWb2CePaTYuSKkteqxa3HcrFSAR7iVLyRptjU0wr/Qka/JZTNek6TWvvC/xRzb9Wu32mU95YaHiCgaxwo8y36Yv2XfSv2esX4k6iqgJcq6GgVrXYH66i8jrWflr235Vti+xY6lDHxi1Q+scRtNSWZIqAbu9dzO4RRU09JW68EfKa4H/AKGb/wATXwa+tOfmXdUyMTHXy7WFj+wMfi1U4Sgb/iN6e00Gbur4A395nfK6vEiyE3A7HbqGdj2nB6rfUlOmsy8xE+WiD8zt669tDZOgJiZ/xtlUZXy+nv8AMx6wFFuRWO+0/wBRA/Lv0Hp+sV1AZKoMN3K0XP3sAeDrx/aZWb0qypPm1E20/wBQ8j9ZtruUktLnyHL2bdHx5a47czDR1Pk1nX+sPpvU16pmWUmohTtqh/SJ40KxftVSzHwAOZ7r4P8AhfNyqMy0Ma7DWa0YDZTf5j+w4/eV8iFfXWI12Lt2AvUsU1Aj51Y3U2/zfaXOndHpyuhUYWX/AA3d2ZT6g+k0MbpVHTaa6a7Gt7OO4wc/BbLuT5NzV3Ujvr5+knfO5zfscZdCOGHqMTonQejdIrryqKbn7fra1dk/pL2B0n4eylV6ei4rKPDmoTyFvxWiWph9S6YtyoQO4nx/uJHWf+IWRiXv03Dw1xFT6e5dePtNMbG34MyTTL3xR8P/AAHgWficjNPSckHZXDf6m/8AhKGP1f4fyMcLgdcGWw47L07Lde59DPlnXLLr8+y+x7LO87LMdzKpyGxclLUJDodzqQfaJvqsml7Peddwa71yMRWBev8AiVa9vUTItPZ0vEr/AKRNXBwcjO6hiZWNXbZTnL30qPqO+QR+gIMvdb+C+u00IU6fYQuywTTa/tMEm1LqZ5J7h5/ov8XPya+3u7sWwAffU0+j9JfH6Wcq5vkW7Ip7x/czZwfhn/6bqovzC5z8hdhQvFYPofvNP8DTmIjW7sNI0u/H9ollneXSIyi2jGw66aazRjVfMLebCvJ/eLyOmM1LUWFRZ+alh7/0megFHy9AAAewiL8Y5V6UjhiRoj0kVHWLk35GjThiXZFXQ1xWtrWx71AZD57f/E9QKrGdrEK2Y5I7GU77ePyt9xPHfEWE+V8UY2VQfmUM616H8rKeR/5noMC6zpPVsmxQ1tORafm1nxr3EyX1RlUvPkVxTWGyEO1A537R1VAJJYcL5i61LZDiltJosNychrDQQraZ28icn62vZR9bj7HVqjBgB+k62otWVA36xVFjVspHke/rNH5lVx8qGPoPBmd+wLyecua3vWtGbRO9Q8LMBynqNh3zoS1bZVj2u7rt9FUHsxnh6M+ynP7yd+V/edCmn7INlsVh9MouXyx5C6nNaWQbfZnlKOoXqu0+pyNhfM23yhWa1IBdkDERJceX6H8tFi2lW57z3Ef2iMm1qBRWo7mcEA/eT89nQt2EAcbg2uBXSWG2J2p9pZXROt6yQUovR+IOxgKzVXa5BtewnWhNbD6V0jPvsubJ769/kHA3/tPL/NZc265iDWF7NH1ljGylx7A6s3y2HCep+81NZk35YzlJvGejxcPowvyKjiKXoYjYJ+pT6xlGL0p670pRaiB3TyD9Rsxeq/iPmk02fQT7Ca6dRTIUtTsJXWQSR5ldtsXHs0HCrkEX2t2ICE8D1ia+8YmTbrZRNwGvCfx1JXuGh9vvLfRLa8jHzMKzRsaskGUwim/BClhqf8KFxAYuCV36Sp0mh8i/MNiqVdAUHsw9RIrawYttdbALSh3uP+G8s24KtpFQfmfyWO5bWnF6CK8mV0+m9OsPTWhZX33H0Uz0oFYVGWwM++xyPG5kdZvy/wDEkxcGjtrtYFvljl/1M0cGpKKLqns73U930+OPYxOQu2MRr8g6GcZhStQ1R/mPpD6lk0YGJ811V797pRjxv3PvOwMpWBfs+kEkD1nnsjHu618VtXZ3LhYei2/U+0FNXZ/9DPwbOLStlH+JdUs2o/Kv9Z9hKeV1Sx+oE6VAyhFUcaHtIzsn8Rksdj5NX01IPA+8xrnfJ6jVUDp1bky+MZN+fRE5Pwy31TF+Vu+l+/s/MR/KZaxM5c3BJHLDyPaOo/DtS4W1LN8WDfmG13T8SoJXjgMfJX1kuUc6kcfJN+ScHpVuZ54CAH29Zk14bfKzAj6x717qj7AnkT0edhrlYKYo0FKdzD9ZmY9BrXHpOjTUdMfcSim1KPj2NmAYmTV0/EOO5UVWAI1foxkV9IGKr5ZUGprho+wI1qI6pjqcxNjahhoz0uJ8vL+H8ip/5SrAe00RlpDzvVMNMvNelz2hlAVvb2mZjdMvqtBr+h0Ovpnosm2pdtpWuGvp9prY2YcSo3LiY5+kHbLyTLIWvcBh47Hso6lmrRmUNj5yN9Fy1nVn2b2/WehzKKsLotYyQxFTdxUerS3b8Sg5NgetAzVga7R5lHJy68rpGRZkqfl1juIHrK7GpSxCdTLq1aQykJQzEk/1H2Ewep9fvwK3SkhGYlVb7TZrzGvwqMhkJLhhWij8oHGphdV6FkZnT1NiGqwuexm8c+8sohHvk/ROpZzEyc/pdT9NRnutIDIvkbHmU8b4b6vhF7L8OwHXpzN3pXf0rptVY0bAAHYestY3Wcg9RrT5pCkkaPtr2jO3rsYLwK4b4M3DoFFfz72C8cBvT7xmG/Ver5Bp6RiPeqeWC7B+/tL2ZiYfULl+fcK7P5qw2g0tUMUprxcS+yinu/8Aacjn34iQsri9kSFKi9L+LT1DB6e6dURSxIWxCAAoP6SOldBTFyLVqcWY+SCCjjgfrK3WOp3r24tZNlNihbu5uSBzvfvH4PUGfFBDfUvH7eko79n2X7Lmv6PP/E3/AA4KWNf0dvmKT9VTHWj9jHW4dnS/h/puHVvuDl7ted/eekbq1o6dapHc4Pcv3MxMy638Or36Dg7bRl8r5SXVgUWWemZYyiTdYbGTld+QI/qOfiYdd2SKfm5bHad42icfm+5nmOn5oXLtddjSEy4c/wDE7VqzYjfmTXkStRcXpYol/pVH47onVbb2ZjkuqlifJ0Tv/OeU6j8M9Nxqan6hnOO4/RjYybtsO/c8KPvzPbY4TC+GlABHfcSB7DXAmA2McrrNOSw3XUu9H+r0l1VrhLQOsFB1CvonQqemrotYVt7h3dqAHez+3mauS64ZQ3OfOt68n3EPpfaMIVLv+E7Dj2J3PEdf6jnDrN9N9dhtDkVLyR2b417x+v8AkRwmeD33T766ci5tACxeT+m9TCtqOViMwPbrI7ifsIzp1mRT06gZadt4X6hv09P8obKApVOAdmYVsG0K2HSTn2Gq8d6/mP7TURlorCVIK0A/KvAmIh/DAsWHcOYI6y2hutWB9jrU3cSyEf5GW+EmtRrX5DgcM2z95Ue52BR27lPlW5BlRusYr7BLKR6kcQkyqbULLahA86M6PaD9HPcbI+Wdj4lFHzTUny1cg9gP0g+4HpJsyFxqwo0B4EBsmsUsVYNr6tDzMl8lshyx8eg9oEkvIjcpGh+KDH82oa2b9TM9B4JjmuKkBZXZfGKxFtXHlL2aAt7V3GJfv0maLyQARrUsB5hlNyN9dXUvrcYxXmeGMsVtEZekXRZJ7tyuGjAxiN4Oj5sRejAZH0sPSFfkWAghT2gekm5bMq42Fj+ks1FHAr1/edJtIUzTnB9L2jQ94a5BA0NanZeNWlmgNfeV1DVtoja+hliSa8ELtFq4mR+IDqeOQfWQ/wD69mtyLhVj+iqeTKdlaLpyA59j4hNmB6/ltQgA8FeIVH9oBzXdMp+iiti39bczv8XKIaw30EaI+0pvSLG2OJtdH/wbLX8F1LE7LDwt1Z0d+ksl1S1+SYn7KdfWXpw/wJs+fib2qN+asn2+32mng/BWX1XDbNTLoTEDfU7A7X9RMrrXwzldFzV7T8/Gc7rtHr9j7Geq/HLhfC2J0ut2Bt3bkEeo3wv/AJiW2KMVKt7pGkvRj0fCvT++zfUDcUHgJrf+co3dBZLmNT6T0GpsYtKUWtkUkkshB9odblm0w1KPvmpe9KpTZgJRZjgj5p48gRtJJPcCd+dy1k4uQckVU1Nb83gEDxN3E+BepW1KTdRWdflYn/wI8rIpbJi+WYlFjs4ckkj1M1cfIZqz3S6fgrq2NUSoouA/os0f7HUz68G9Mj5V6NUq8sxmWfWXor875By6rMpVdELIv83pKQufFJPp4IPrPTE1nGK1L2og0AJkvijKs+tdL6/cSReeDRGDfoRg4i5RbLqprqJOu7Xn7iavVb8+vo1dXTMl8W2llYMjEE63FhmpKVIgCKNADwBL11Ni4YuKbU+ntElZL7E/6NkKnhkL8U9TP+HjO7PmWXhLXA7e4b+09Sb+29fPIIM8n1DGxssUBW1alyOgHjgjY/tN/MvdOodNC1O1d+Q62sq7CKV0CfbkiWWVqxJr35HdLCD09VN2HeQmTST8p/cD/WI6307p/Gdm2MCFAKVfmsYDwD6frHP00DL/ABKWfRU3MRlW09Tr7TsFTxv1grWeRXT/AMHj8rrt9txWuqvHx1Olx0H06/6t8sfcmUcnAxum0nqORWHW4/8ApMZvB92b3UHjXrPWdU6L0/Iw6/pGPmDjjgN+omR1Toz9Vz13YFx8dRWievYP950qrEiKqSPQ9O/4g9X6L8G9L6mBRc9mXdSamqCqa1A0B267efb/ADnvsX4rTqGPh2XYl2HflLv5ZYMB+/8A/KfPMTAwMXp/SMG4rdVhmy6zu5+tzv8AynsPlrlfLvTfdUp+Vse40P8AWU3zS/ghvpTZPxB1Kq3GZPnL8xzpQTzx7Sj053opAJ7i3OjPO2dPzk6jWhV7GrpAJbx3E8z1NF2P0jFQ3EXZhHj+VJlz8uwyqSLwxltq72/htrZBlbDRBlPb3BjWhImN1TNzLibkLAEcnegZV6FmXi20XrruXSg+sS+Vjg8G+sX0qyyi29nHPzTYN+m5rJ1HHyEe0oTYralQ3/8AqGreoAy2BQMclKjvyde8RT/YFS/6NXoeUl9jEjkgiMuCVLYmiwD8TF6XlLjZOzx3Hiab9QNRcqoJZjqZpw7CS42rWdvVewrkD7GJuubGCsRosNiOo6vaa3RsixDrx6TDzMo23E95f7yp8WOamYnxn+h+VmNlPWXA2rAnXrKnSvh1M7OzrC7fKxbGIbx3E8/21EPkCqwB+Aw4Jm70vN1025E/5hc7+415mitOuLRohx2JqWrGp+YeG32qfUCB1pbMTBxsquxh2HtJPt5E0KMumhSypWbl8Ow3qU8nqluYt9XUUWyixCofWte2osFj1lq4zIq6+b8Bq113MQSYVmeQiXXHsx6VLOxHhfeZeLTiDtprYhtb5PpHYOYXszWyatDv1XUy7BTx49Zb116xHx5Gnk5VaY9T4yq4c94LexlOzJtycK0Y7ENoop9ZnnHuxKa7cdgcRdqKT5UE+h9hLNVqfKFtR+pj+X3knDyH6GIycqlOmUnJss+dZb8tQq8s445HoJ6FLWHRFQqEt12uAf7TKuNF6oclQHqfvTY8N9oYzWsXsrUuh5Zh6Si6PbEhvokc9/8A6Y1g/UBtR7+8sfD+Qasy3KtBCrWQfsCPP95j1ZKHIJJ4GxNSzWH0fR38207I9x4EeNfUX6JFa+iq8ZB7m7GB7dHXMr9BuI6Xk4bcXVWAdv8AeMwWfIW5DUy9vv7SMG/Ax+o23Vm/5x0LxYAE48Fdf+ZbFfi0K+Oy9jZt34uvHB2rDWx5l1bkoW4sB21sF/uZXU0Yq35qkFvyU/v5Mp0k2tfQDyQGAP8AMZnlHWmRU4aVd5x+qsbDqhRoH/8AP1lm64ILLKlHdZvRHqfeZuTaqO/evd2ojFfca+ofrqPajVPYL/oQkq59V17QqHnRvpAqwq7Q1d+wRzx/eJNmNXmC7GUXUflIVeQfUmCtzXHIqdiHZdBhM9s+vo7imoF1J/ign8w9polsiyVbY1MtU6k3ysZ7cc8H5S7EalFd3WqqXJI4sXR417RuHmU01m/B22G2w1bH60b/AMidi21/j6LT/SVJlc44it0M0OpZC0s9rEgH6V17Ty3T+vk5oxn/ACEFdHyfvNXrN5tR0HGjwPeee6dh1Wbudf4ik+fIk41UVH8kM6PBv5WWrUhieK+SZf8AhvKazAybLBr5/Cb9h6zzGOFy3bHttKVs/wBbey+s2Wayt1dF+TXwEAPAHpHcFET6WhuVeLbmfQ3vUjqnVzR0/AdtlTaysq+uhxFdUurUK9StZaw2yKP85l9bXfRcVq3DZNVoeysegI/8QVw7S8h+pm0wpbtvJ27jevbcinJW5nxb9N81TSFX1bR1/nqVentrHS1zoBd8+v2g9PsWvrWGDWWVW+aG9O4ng/tB08jKhMGrrN/w5VX0/H7Hyql7brmUEIx8qo8ceCfMVb8QZ/Uz8nMt+avlT2gHfoOInrlv4zqtlrVJWVAQlRrv1/Mfvz/lKIFtersfkoQTL1GL84H6Uj0HSrKM3pt34hWS4Mewnjkemo/DyMd1+X8oJYh5YjmZmNfV1LIqrsU12K/erAcH7RrZanIetsdxY+wbCNCI4efAyqQq3Np/H5G6ylq6Om1yPcQ+kZd+R1ELZoBd92hr9I3Asx7qg99Nb2qO1yU3yP8AxHKcaiz51Hq+mX0A+0SyC6+ix1LBfV3K5PGyCPT1mR8N9afKu1UTonVin01NbOYHJpcccyOlY1OMt3y6kQs5b6V1uJW4xqaa8g6LC5mZz0diV672Yb+wi+quzYFnZyTwNTPsu70FpHc45I9juWbL/l4Ndli/SfeGMfWifWjO6fSf8NzbgNtSnP8A5jenZL2XVsoAJB3r2hdDya2zc/GsGq7QSoP8w9R/aWMDEpwMfIVre6zZVf8As9P7y6USdDVvuKfD3aNErZ38/fj/AGmbiZXdU7sNFf8AOG7nJxlVd/LeuwEjwDxqV2Cr8xU1ogaI9ZV10WURVme2O7kWFEPJImWOv0ZdyCu9HsVvB/Mv6RudjPdjOoDbZT9OudiZPTfhmuuxcjIuYXA9yKOOZqqhUoNyfkpcT1TZhYDvH1ASs/UmVSVPPtqDjr85Gss7gd+DAequns02yTs79Jl6pMpZSOTlGwqzdyueT7SLrWC9qeYzJADbTkEb4lRm2OBzL0k/OAwjbH+G/g86HrHonYhY8H0AhVYwrU2WHbnwPaQgLv2k8Qt/0TqmFUvft1Y1sPUesZV/yx3DTbi2Uox1vUe4Kdp8+8WU20RVxX6DQ9w2OYfkAkQK1J5U+YYb6+wjmVBxIcifMrBPnccq/VqQqEDQEYoI9ICIYqx1a6i0Ij1itjhqsaBBTmOQbIErl5Aj/9k=
        headers:
          content-type: image/jpeg
          content-disposition: 'attachment; filename="u=*********,4182719556&fm=253&app=138&f=JPEG.jpg"'
          cache-control: 'no-cache, private'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/sts/credentials
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取STS临时凭证
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      filename:
        name: filename
        description: 文件名
        required: true
        example: example.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      filesize:
        name: filesize
        description: 文件大小（字节）
        required: true
        example: 1024000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      mime_type:
        name: mime_type
        description: MIME类型
        required: true
        example: application/pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      md5_hash:
        name: md5_hash
        description: 文件MD5值（用于秒传）
        required: false
        example: 5d41402abc4b2a76b9719d911017c592
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      filename: example.pdf
      filesize: 1024000
      mime_type: application/pdf
      md5_hash: 5d41402abc4b2a76b9719d911017c592
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "quick_upload": false,
            "upload_id": "550e8400-e29b-41d4-a716-446655440000",
            "credentials": {
              "AccessKeyId": "STS.xxx",
              "AccessKeySecret": "xxx",
              "SecurityToken": "xxx",
              "Expiration": "2025-08-04T12:00:00Z"
            },
            "region": "cn-hangzhou",
            "bucket": "my-bucket",
            "endpoint": "https://oss-cn-hangzhou.aliyuncs.com",
            "prefix": "attachments/"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/sts/confirm
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 确认上传完成
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      upload_id:
        name: upload_id
        description: 上传ID
        required: true
        example: 550e8400-e29b-41d4-a716-446655440000
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      object_key:
        name: object_key
        description: OSS对象键值
        required: true
        example: attachments/2025/08/04/xxx.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      filename:
        name: filename
        description: 文件名（可选）
        required: false
        example: example.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      filesize:
        name: filesize
        description: 文件大小（可选）
        required: false
        example: 1024000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      mime_type:
        name: mime_type
        description: MIME类型（可选）
        required: false
        example: application/pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      upload_id: 550e8400-e29b-41d4-a716-446655440000
      object_key: attachments/2025/08/04/xxx.pdf
      filename: example.pdf
      filesize: 1024000
      mime_type: application/pdf
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":"","file_name":"modi.png","file_path":"uploads\/5e4f00df-4238-35bd-9edc-0b98dc359c80.png","file_size":5161316,"mime_type":"image\/png","storage_type":"local","md5_hash":null,"file_url":"http:\/\/localhost:8005\/storage\/uploads\/5e4f00df-4238-35bd-9edc-0b98dc359c80.png","formatted_file_size":"4.92 MB","created_at":null,"updated_at":null,"relation_id":null,"attachable_type":null,"attachable_id":null,"category":null,"description":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/attachments/by-business
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 根据业务ID获取附件列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      attachable_type:
        name: attachable_type
        description: 业务类型
        required: true
        example: App\Models\Entity
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: 业务ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      category:
        name: category
        description: 附件分类
        required: false
        example: contract
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      attachable_type: App\Models\Entity
      attachable_id: 1
      category: contract
    bodyParameters:
      attachable_type:
        name: attachable_type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: ''
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category:
        name: category
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      attachable_type: architecto
      attachable_id: 16
      category: architecto
    fileParameters: []
    responses:
      -
        status: 404
        content: '{"message":"No query results for model [App\\Models\\Attachment] by-business"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/attachments/update-by-relation/{attachmentRelation_id}'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 更新文件关联描述
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      attachmentRelation_id:
        name: attachmentRelation_id
        description: 'The ID of the attachmentRelation.'
        required: true
        example: 9
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachable_relation_id:
        name: attachable_relation_id
        description: 附件关联ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      attachmentRelation_id: 9
      attachable_relation_id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      description:
        name: description
        description: 描述
        required: false
        example: 这是我的描述
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      description: 这是我的描述
    fileParameters: []
    responses:
      -
        status: 201
        content: '{"message":"更新成功"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
