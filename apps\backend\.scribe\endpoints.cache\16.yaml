## Autogenerated by <PERSON>ri<PERSON>. DO NOT MODIFY.

name: 考勤配置
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/checkin-configs
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取考勤配置列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      attachable_type:
        name: attachable_type
        description: 所属模块
        required: false
        example: App\Models\Lifecycle
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keyword:
        name: keyword
        description: 搜索关键词
        required: false
        example: 早班
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页记录数
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      attachable_type: App\Models\Lifecycle
      keyword: 早班
      page: 1
      per_page: 15
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":18,"attachable_type":"App\\Models\\Lifecycle","attachable_id":17,"checkin_time":1756092023,"status":1,"is_photo":1,"location":"Debug Test Location","longitude":"116.407400","latitude":"39.904200","location_range":150,"created_at":1756092023,"updated_at":1756092023,"users":[],"attachable":null},{"id":17,"attachable_type":"App\\Models\\Lifecycle","attachable_id":15,"checkin_time":1629936000,"status":1,"is_photo":1,"location":"公司大门","longitude":"39.904200","latitude":"116.407400","location_range":100,"created_at":1756090821,"updated_at":1756092779,"users":[{"id":1,"nickname":"admin","pivot":{"checkin_config_id":17,"user_id":1}},{"id":2,"nickname":"admin","pivot":{"checkin_config_id":17,"user_id":2}},{"id":3,"nickname":"admin","pivot":{"checkin_config_id":17,"user_id":3}}],"attachable":{"id":15,"type":"test_workflow","date":1756090821,"initiator_id":1,"content":"Test lifecycle for complete workflow","acceptance_entity_id":null,"acceptance_personnel_id":null,"acceptance_time":null,"created_by":1,"updated_by":1,"is_checked":null,"is_need_attendance":null,"created_at":2025,"updated_at":2025,"deleted_at":null,"follow_ups_tags":[],"progress":0,"tags":[]}},{"id":3,"attachable_type":"App\\Models\\Lifecycle","attachable_id":1,"checkin_time":1629936000,"status":1,"is_photo":1,"location":"公司大门","longitude":"39.904200","latitude":"116.407400","location_range":100,"created_at":1755955048,"updated_at":1755955048,"users":[{"id":1,"nickname":"admin","pivot":{"checkin_config_id":3,"user_id":1}},{"id":2,"nickname":"admin","pivot":{"checkin_config_id":3,"user_id":2}},{"id":3,"nickname":"admin","pivot":{"checkin_config_id":3,"user_id":3}}],"attachable":null},{"id":2,"attachable_type":"App\\Models\\Lifecycle","attachable_id":1,"checkin_time":1629936000,"status":1,"is_photo":1,"location":"公司大门","longitude":"39.904200","latitude":"116.407400","location_range":100,"created_at":1755854654,"updated_at":1756086890,"users":[{"id":1,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":1}},{"id":2,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":2}},{"id":3,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":3}}],"attachable":null}],"links":{"first":"http://localhost:8005/api/admin/checkin-configs?page=1","last":"http://localhost:8005/api/admin/checkin-configs?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http://localhost:8005/api/admin/checkin-configs?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"http://localhost:8005/api/admin/checkin-configs","per_page":15,"to":4,"total":4}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/checkin-configs
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建考勤配置
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      attachable_type:
        name: attachable_type
        description: 所属模块
        required: true
        example: App\Models\Lifecycle
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: 关联业务ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location:
        name: location
        description: 打卡地点
        required: false
        example: 公司大门
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      longitude:
        name: longitude
        description: 打卡位置经度
        required: false
        example: '39.9042'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      latitude:
        name: latitude
        description: 打卡位置纬度
        required: false
        example: '116.4074'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      checkin_time:
        name: checkin_time
        description: 打卡时间
        required: true
        example: 1629936000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location_range:
        name: location_range
        description: 打卡范围(米)
        required: false
        example: 100
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_photo:
        name: is_photo
        description: '是否拍照(0-否,1-是)'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      user_ids:
        name: user_ids
        description: 打卡人员ID列表
        required: true
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      attachable_type: App\Models\Lifecycle
      attachable_id: 1
      location: 公司大门
      longitude: '39.9042'
      latitude: '116.4074'
      checkin_time: 1629936000
      location_range: 100
      is_photo: 1
      user_ids:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 201
        content: '{"id":20,"attachable_type":"App\\Models\\Lifecycle","attachable_id":1,"checkin_time":1629936000,"status":null,"is_photo":1,"location":"公司大门","longitude":"39.9042","latitude":"116.4074","location_range":100,"created_at":1756102115,"updated_at":1756102115,"users":[{"id":1,"nickname":"admin","pivot":{"checkin_config_id":20,"user_id":1}},{"id":2,"nickname":"admin","pivot":{"checkin_config_id":20,"user_id":2}},{"id":3,"nickname":"admin","pivot":{"checkin_config_id":20,"user_id":3}}],"attachable":null}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/checkin-configs/{checkinConfig}'
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新考勤配置
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      checkinConfig:
        name: checkinConfig
        description: 配置ID
        required: true
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      checkinConfig: 2
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      attachable_type:
        name: attachable_type
        description: 所属模块
        required: false
        example: App\Models\Lifecycle
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: 关联业务ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location:
        name: location
        description: 打卡地点
        required: false
        example: 公司大门
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      longitude:
        name: longitude
        description: 打卡位置经度
        required: false
        example: '39.9042'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      latitude:
        name: latitude
        description: 打卡位置纬度
        required: false
        example: '116.4074'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      checkin_time:
        name: checkin_time
        description: 打卡时间
        required: false
        example: 1629936000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location_range:
        name: location_range
        description: 打卡范围(米)
        required: false
        example: 100
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_photo:
        name: is_photo
        description: '是否拍照(0-否,1-是)'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      user_ids:
        name: user_ids
        description: 打卡人员ID列表
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      attachable_type: App\Models\Lifecycle
      attachable_id: 1
      location: 公司大门
      longitude: '39.9042'
      latitude: '116.4074'
      checkin_time: 1629936000
      location_range: 100
      is_photo: 1
      user_ids:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":2,"attachable_type":"App\\Models\\Lifecycle","attachable_id":1,"checkin_time":1629936000,"status":1,"is_photo":1,"location":"公司大门","longitude":"39.9042","latitude":"116.4074","location_range":100,"created_at":1755854654,"updated_at":1756102115,"users":[{"id":1,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":1}},{"id":2,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":2}},{"id":3,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":3}}],"attachable":null}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/checkin-configs/{checkinConfig}'
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取考勤配置详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      checkinConfig:
        name: checkinConfig
        description: 配置ID
        required: true
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      checkinConfig: 2
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":2,"attachable_type":"App\\Models\\Lifecycle","attachable_id":1,"checkin_time":1629936000,"status":1,"is_photo":1,"location":"公司大门","longitude":"39.904200","latitude":"116.407400","location_range":100,"created_at":1755854654,"updated_at":1756086890,"users":[{"id":1,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":1}},{"id":2,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":2}},{"id":3,"nickname":"admin","pivot":{"checkin_config_id":2,"user_id":3}}],"attachable":null}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
