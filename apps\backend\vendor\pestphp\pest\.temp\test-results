{"version": "pest_3.8.2", "defects": {"Tests\\Feature\\RoleMenuPermissionTest::test_admin_role_has_all_permissions": 8, "Tests\\Feature\\RoleMenuPermissionTest::test_user_role_has_limited_permissions": 8, "Tests\\Feature\\RoleMenuPermissionTest::test_user_can_check_menu_access": 8, "Tests\\Feature\\RoleMenuPermissionTest::test_user_can_check_menu_permission": 8, "Tests\\Feature\\RoleMenuPermissionTest::test_role_menu_permission_api_endpoints": 8, "AssetTest::test_asset_can_be_created_with_category_ids": 8, "AssetTest::test_asset_category_accessor_returns_categories": 8, "AssetTest::test_asset_can_load_relationships_without_error": 8, "Tests\\Feature\\CheckinConfigRouteBindingTest::test_checkin_config_route_binding_works": 8, "Tests\\Feature\\CheckinConfigRouteBindingTest::test_checkin_config_route_binding_fails_for_nonexistent_id": 8, "Tests\\Feature\\ExportControllerTest::it_can_export_asset_template": 8, "Tests\\Feature\\ExportControllerTest::it_can_export_category_template": 8, "Tests\\Feature\\ExportControllerTest::it_can_export_category_template_with_sample": 8, "Tests\\Feature\\ExportControllerTest::it_can_export_entity_template": 8, "Tests\\Feature\\ExportControllerTest::it_can_export_user_template": 8, "Tests\\Feature\\ExportControllerTest::it_rejects_unsupported_template_type": 8, "Tests\\Feature\\ExportControllerTest::it_can_get_supported_export_types": 8, "Tests\\Feature\\ExportControllerTest::it_can_export_asset_data": 8, "Tests\\Feature\\ExportControllerTest::it_rejects_unsupported_data_export_type": 8, "Tests\\Feature\\ExportControllerTest::it_returns_error_for_unsupported_data_export": 8, "Tests\\Feature\\ExportControllerTest::it_can_get_export_history": 8, "Tests\\Feature\\ExportControllerTest::it_can_filter_export_history_by_type": 8, "Tests\\Feature\\ExportControllerTest::it_can_filter_export_history_by_status": 8, "Tests\\Feature\\ExportControllerTest::it_can_download_export_file": 8, "Tests\\Feature\\ExportControllerTest::it_prevents_downloading_other_users_files": 8, "Tests\\Feature\\ExportControllerTest::it_returns_404_for_non_existent_export_file": 8, "Tests\\Feature\\ImportControllerTest::it_can_create_import_task_for_asset": 8, "Tests\\Feature\\ImportControllerTest::it_can_create_import_task_for_category": 8, "Tests\\Feature\\ImportControllerTest::it_can_create_import_task_for_entity": 8, "Tests\\Feature\\ImportControllerTest::it_can_create_import_task_for_user": 8, "Tests\\Feature\\ImportControllerTest::it_rejects_unsupported_import_type": 8, "Tests\\Feature\\ImportControllerTest::it_can_get_import_task_status": 8, "Tests\\Feature\\ImportControllerTest::it_returns_404_for_non_existent_task": 8, "Tests\\Feature\\ImportControllerTest::it_can_get_import_task_list": 8, "Tests\\Feature\\ImportControllerTest::it_validates_task_type_in_status_endpoint": 8}, "times": {"P\\Tests\\Unit\\ExampleTest::__pest_evaluable_that_true_is_true": 0.012, "P\\Tests\\Feature\\ExampleTest::__pest_evaluable_the_application_returns_a_successful_response": 0.05}}