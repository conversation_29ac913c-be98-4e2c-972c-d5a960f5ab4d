<template>
  <div class="lifecycle-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @reset="handleReset"
      @search="handleSearch"
      :showExpand="true"
    />

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton
            type="primary"
            @click="showLifecycleDialog('add')"
            v-ripple
            v-if="hasAuth('add')"
          >
            <ElIcon><Plus /></ElIcon>
            新增生命周期
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="tableConfig"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 关联资产列插槽 -->
        <template #asset_id="{ row }">
          <div v-if="row.assets && row.assets.length > 0">
            <ElPopover placement="right" width="500" trigger="click">
              <template #reference>
                <ElButton type="primary" link>
                  <ElIcon><Collection /></ElIcon>
                  {{ row.assets.length }}个资产
                </ElButton>
              </template>
              <template #default>
                <div>
                  <h4 style="margin: 0 0 10px; font-size: 14px; color: var(--el-text-color-primary)"
                    >关联资产列表</h4
                  >
                  <ElTable
                    :data="row.asset_details || row.assets"
                    size="small"
                    max-height="300"
                    stripe
                  >
                    <ElTableColumn prop="id" label="ID" width="60" />
                    <ElTableColumn prop="name" label="名称" show-overflow-tooltip min-width="100" />
                    <ElTableColumn label="品牌" width="80" show-overflow-tooltip>
                      <template #default="{ row }">
                        {{ row.brand?.name || row.brand?.display_name || '-' }}
                      </template>
                    </ElTableColumn>
                    <ElTableColumn prop="model" label="型号" width="80" show-overflow-tooltip />
                    <ElTableColumn
                      prop="serial_number"
                      label="序列号"
                      width="100"
                      show-overflow-tooltip
                    />
                  </ElTable>
                </div>
              </template>
            </ElPopover>
          </div>
          <span v-else>-</span>
        </template>
        <!-- 进度列插槽 -->
        <template #progress="{ row }">
          <ElProgress
            :percentage="row.progress || 0"
            :color="getProgressColor(row.progress || 0)"
            :stroke-width="18"
            text-inside
            :format="() => formatProgress(row.progress) + '%'"
          />
        </template>
        <!-- 验收状态列插槽 -->
        <template #is_checked="{ row }">
          <ElTag :type="row.is_checked ? 'success' : 'info'">
            {{ row.is_checked ? '已验收' : '未验收' }}
          </ElTag>
        </template>
        <!-- 考勤状态列插槽 -->
        <template #is_need_attendance="{ row }">
          <ElTag v-if="row.is_need_attendance === 1" type="primary" size="small"> 需要 </ElTag>
          <span v-else class="text-muted">-</span>
        </template>
        <!-- 操作列插槽 -->
        <template #operation="{ row }">
          <div style="display: flex; flex-wrap: wrap; gap: 5px">
            <ArtButtonTable type="primary" @click="showFollowUpDialog(row)"> 跟进 </ArtButtonTable>
            <!-- 考勤配置按钮：需要考勤管理时显示 -->
            <ArtButtonTable
              v-if="row.is_need_attendance === 1"
              type="primary"
              plain
              size="small"
              @click="openAttendanceConfig(row)"
            >
              考勤配置
            </ArtButtonTable>
            <!-- 验收按钮：进度100且未验收时显示 -->
            <ArtButtonTable
              v-if="canAccept(row)"
              type="success"
              @click="handleAccept(row)"
              title="验收此生命周期"
            >
              验收
            </ArtButtonTable>
            <ArtButtonTable
              type="edit"
              @click="showLifecycleDialog('edit', row)"
              v-if="hasAuth('edit')"
            />
            <ArtButtonTable type="view" @click="showDetail()" v-if="hasAuth('view')" />
            <ArtButtonTable type="delete" @click="handleDelete(row)" v-if="hasAuth('delete')" />
          </div>
        </template>
      </ArtTable>

      <!-- 生命周期对话框 -->
      <LifecycleDialog
        v-model="lifecycleDialogVisible"
        :type="dialogType"
        :data="currentLifecycle"
        @success="handleLifecycleSuccess"
      />

      <!-- 跟进信息对话框 -->
      <FollowUpDialog
        v-model="followUpDialogVisible"
        :lifecycleId="currentLifecycleId"
        :lifecycle="currentLifecycle"
        @success="handleFollowUpSuccess"
      />

      <!-- 考勤配置弹窗 -->
      <AttendanceConfigDialog
        v-model="attendanceDialogVisible"
        :type="attendanceDialogType"
        :data="currentAttendanceConfig"
        :available-user-ids="availableUserIds"
        :title="`生命周期 - 考勤配置`"
        @success="handleAttendanceConfigSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'Lifecycle' })

  // Vue 核心
  import { ref, computed, onMounted } from 'vue'

  // UI 框架
  import {
    ElButton,
    ElMessage,
    ElMessageBox,
    ElIcon,
    ElProgress,
    ElTag,
    ElPopover,
    ElTable,
    ElTableColumn
  } from 'element-plus'
  import { Plus, Collection } from '@element-plus/icons-vue'

  // 工具函数
  import { formatDate } from '@/utils/dataprocess/format'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'
  import { useDictionaryStore } from '@/store/modules/dictionary'
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import LifecycleDialog from './components/LifecycleDialog.vue'
  import FollowUpDialog from './components/FollowUpDialog.vue'
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import { AttendanceConfigDialog } from '@/components/custom/attendance'

  // API
  import {
    getLifecycleList,
    deleteLifecycle,
    getLifecycleDetail,
    acceptLifecycle
  } from '@/api/admin/asset'
  import {
    getCheckinConfigs,
    createCheckinConfig,
    updateCheckinConfig
  } from '@/api/admin/attendance'
  import type { Lifecycle } from '@/types/api/lifecycle'
  import { getEntityList } from '@/api/admin/entity'
  import { getUserList } from '@/api/admin/user'

  // 类型定义
  import type { SearchFormItem } from '@/types'

  // 表格配置
  const tableConfig = {
    rowKey: 'id',
    height: undefined,
    maxHeight: '600px'
  }

  // 权限控制
  const { hasAuth } = useAuth()

  // 字典数据
  const dictionaryStore = useDictionaryStore()
  const lifecycleTypes = ref<Array<{ code: string; value: string }>>([])
  const userList = ref<any[]>([])
  const entityList = ref<any[]>([])

  // 对话框
  const lifecycleDialogVisible = ref(false)
  const followUpDialogVisible = ref(false)
  const dialogType = ref<'add' | 'edit'>('add')
  const currentLifecycle = ref<Lifecycle | null>(null)
  const currentLifecycleId = ref<number>(0)

  // 考勤配置相关状态
  const attendanceDialogVisible = ref(false)
  const attendanceDialogType = ref<'add' | 'edit'>('add')
  const currentAttendanceConfig = ref<any>(null)

  // 计算可选人员（发起人+协助人员）
  const availableUserIds = computed(() => {
    if (!currentLifecycle.value) {
      return []
    }

    const ids = new Set<number>()

    // 发起人（后端返回的字段名是 initiator，不是 initiator_id）
    if (currentLifecycle.value.initiator) {
      ids.add(currentLifecycle.value.initiator)
    }

    // 协助人员
    currentLifecycle.value.assistants?.forEach((id) => ids.add(id))

    return Array.from(ids)
  })

  // 搜索表单状态
  const searchFormState = ref({
    type: null,
    initiator: null,
    acceptanceEntity: null
  })

  // 搜索表单配置
  const searchFormItems = computed<SearchFormItem[]>(() => [
    {
      label: '类型',
      prop: 'type',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择类型'
      },
      options: lifecycleTypes.value.map((item: any) => ({
        label: item.value,
        value: item.code
      }))
    },
    {
      label: '发起人',
      prop: 'initiator',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择发起人'
      },
      options: userList.value.map((item: any) => ({
        label: item.nickname || item.nickName || item.realName,
        value: item.id
      }))
    },
    {
      label: '验收相关方',
      prop: 'acceptanceEntity',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择验收相关方'
      },
      options: entityList.value.map((item: any) => ({
        label: item.name,
        value: item.id
      }))
    }
  ])

  // 使用 useTable composable
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    onPageSizeChange,
    onCurrentPageChange,
    searchState,
    searchData,
    resetSearch
  } = useTable<Lifecycle>({
    core: {
      apiFn: async (params: any) => {
        // 处理参数格式
        const searchParams: any = {}
        if (params.type) {
          searchParams.type = params.type
        }
        if (params.initiator) {
          searchParams.initiator_id = params.initiator
        }
        if (params.acceptanceEntity) {
          searchParams.acceptance_entity_id = params.acceptanceEntity
        }

        const response = await getLifecycleList({
          page: params.current,
          per_page: params.size,
          ...searchParams
        })

        return {
          records: response.data || [],
          total: response.meta?.total || 0,
          current: params.current,
          size: params.size
        }
      },
      apiParams: {
        current: 1,
        size: 10,
        type: '',
        initiator: '',
        acceptanceEntity: ''
      },
      columnsFactory: () => [
        { prop: 'id', label: '周期表ID', width: 100 },
        {
          prop: 'asset_id',
          label: '关联资产',
          width: 120,
          showOverflowTooltip: true,
          useSlot: true,
          slotName: 'asset_id'
        },
        {
          prop: 'type',
          label: '类型',
          width: 120,
          formatter: (row: Lifecycle) => {
            const type = lifecycleTypes.value.find((item: any) => item.code === row.type)
            return type ? type.value : row.type
          }
        },
        {
          prop: 'date',
          label: '日期',
          width: 120,
          formatter: (row: Lifecycle) => (row.date ? formatDate(row.date, 'YYYY-MM-DD') : '-')
        },
        {
          prop: 'initiator',
          label: '发起人',
          width: 100,
          formatter: (row: Lifecycle) => {
            const user = userList.value.find((item) => item.id === row.initiator)
            return user ? user.nickname || user.nickName || user.realName : '-'
          }
        },
        { prop: 'content', label: '内容', minWidth: 200, showOverflowTooltip: true },
        {
          prop: 'progress',
          label: '任务完成进度',
          width: 150,
          useSlot: true,
          slotName: 'progress'
        },
        {
          prop: 'is_checked',
          label: '验收状态',
          width: 100,
          useSlot: true,
          slotName: 'is_checked'
        },
        {
          prop: 'is_need_attendance',
          label: '考勤',
          width: 80,
          align: 'center',
          useSlot: true,
          slotName: 'is_need_attendance'
        },
        {
          prop: 'acceptance_entity_name',
          label: '验收相关方',
          width: 150,
          formatter: (row: Lifecycle) => row.acceptance_entity_name || '-'
        },
        {
          prop: 'acceptance_personnel_name',
          label: '验收人员',
          width: 100,
          formatter: (row: Lifecycle) => row.acceptance_personnel_name || '-'
        },
        {
          prop: 'acceptance_time',
          label: '验收日期',
          width: 120,
          formatter: (row: Lifecycle) =>
            row.acceptance_time ? formatDate(row.acceptance_time, 'YYYY-MM-DD') : '-'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 300,
          fixed: 'right',
          useSlot: true
        }
      ],
      immediate: true // 确保初始时加载数据
    }
  })

  // 初始化 - 只为搜索表单加载必要的数据
  onMounted(async () => {
    // 搜索表单需要这些数据
    await loadDictionaries()
    await loadUserList()
    await loadEntityList()
  })

  // 加载字典数据
  const loadDictionaries = async () => {
    const data = await dictionaryStore.fetchItemsByCode('lifecycle_config')
    lifecycleTypes.value = data || []
  }

  // 加载用户列表
  const loadUserList = async () => {
    const response = await getUserList({ current: 1, size: 1000 })
    userList.value = response.data || []
  }

  // 加载相关方列表
  const loadEntityList = async () => {
    const response = await getEntityList({ current: 1, size: 1000 })
    entityList.value = response.data || []
  }

  // 处理搜索
  const handleSearch = () => {
    searchState.type = searchFormState.value.type
    searchState.initiator = searchFormState.value.initiator
    searchState.acceptanceEntity = searchFormState.value.acceptanceEntity
    searchData()
  }

  // 处理重置
  const handleReset = () => {
    searchFormState.value = {
      type: null,
      initiator: null,
      acceptanceEntity: null
    }
    resetSearch()
  }

  // 显示生命周期对话框
  const showLifecycleDialog = async (type: 'add' | 'edit', row?: Lifecycle) => {
    dialogType.value = type
    if (type === 'edit' && row?.id) {
      // 编辑时获取详情数据（包含附件详情）
      const detail = await getLifecycleDetail(row.id)
      currentLifecycle.value = detail
    } else {
      currentLifecycle.value = null
    }
    lifecycleDialogVisible.value = true
  }

  // 显示跟进对话框
  const showFollowUpDialog = (row: Lifecycle) => {
    currentLifecycleId.value = row.id!
    currentLifecycle.value = row // 传递完整的生命周期数据
    followUpDialogVisible.value = true
  }

  // 显示详情
  const showDetail = () => {
    // TODO: 实现详情页面
    ElMessage.info('详情功能开发中')
  }

  // 删除
  const handleDelete = async (row: Lifecycle) => {
    try {
      await ElMessageBox.confirm('确定要删除该生命周期记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await deleteLifecycle(row.id!)
      ElMessage.success('删除成功')
      refreshAfterRemove()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 生命周期对话框成功
  const handleLifecycleSuccess = () => {
    if (dialogType.value === 'add') {
      refreshAfterCreate()
    } else {
      refreshAfterUpdate()
    }
  }

  // 打开考勤配置
  const openAttendanceConfig = async (row: Lifecycle) => {
    currentLifecycle.value = row

    // 查询该生命周期的考勤配置
    try {
      const params: any = {
        attachable_type: 'App\\Models\\Lifecycle',
        attachable_id: row.id,
        page: 1,
        page_size: 1
      }
      const response = await getCheckinConfigs(params)

      if (response.data && response.data.length > 0) {
        attendanceDialogType.value = 'edit'
        currentAttendanceConfig.value = response.data[0]
      } else {
        attendanceDialogType.value = 'add'
        currentAttendanceConfig.value = null
      }
    } catch (error) {
      attendanceDialogType.value = 'add'
      currentAttendanceConfig.value = null
    }

    attendanceDialogVisible.value = true
  }

  // 处理考勤配置成功
  const handleAttendanceConfigSuccess = async (formData: any) => {
    // 组装完整数据
    const fullData = {
      ...formData,
      attachable_type: 'App\\Models\\Lifecycle',
      attachable_id: currentLifecycle.value!.id
    }

    try {
      if (attendanceDialogType.value === 'add') {
        await createCheckinConfig(fullData)
        ElMessage.success('考勤配置创建成功')
      } else {
        await updateCheckinConfig(currentAttendanceConfig.value!.id, fullData)
        ElMessage.success('考勤配置更新成功')
      }

      // 刷新列表
      await refreshAfterUpdate()
    } catch (error) {
      console.error('考勤配置保存失败:', error)
    }
  }

  // 格式化进度百分比，保留2位小数
  const formatProgress = (progress: number | undefined) => {
    if (!progress) return '0'
    return progress.toFixed(2)
  }

  // 进度条颜色配置
  const getProgressColor = (percentage: number) => {
    if (percentage >= 95) {
      return '#67c23a' // 绿色 - 接近完成
    } else if (percentage >= 70) {
      return '#e6a23c' // 橙色 - 进行中
    } else if (percentage >= 30) {
      return '#409eff' // 蓝色 - 开始阶段
    } else {
      return '#f56c6c' // 红色 - 刚开始
    }
  }

  // 判断是否可以验收
  const canAccept = (row: Lifecycle) => {
    // 进度100且未验收时显示验收按钮
    return (row.progress || 0) === 100 && !row.is_checked
  }

  // 处理验收
  const handleAccept = async (row: Lifecycle) => {
    try {
      await ElMessageBox.confirm(
        `确定要验收生命周期"${row.content}"吗？验收后状态将标记为已验收，且无法撤销。`,
        '验收确认',
        {
          confirmButtonText: '确定验收',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await acceptLifecycle(row.id!)
      ElMessage.success('验收成功')

      // 刷新表格数据
      refreshAfterUpdate()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('验收失败:', error)
      }
    }
  }

  // 跟进对话框成功
  const handleFollowUpSuccess = () => {
    // 跟进记录可能影响进度，刷新数据
    refreshAfterUpdate()
  }
</script>

<style lang="scss" scoped>
  .lifecycle-page {
    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  // 多资产显示样式
  .asset-notes {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
</style>
