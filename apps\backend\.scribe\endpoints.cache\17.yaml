## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: 考勤记录
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/checkin-records
    metadata:
      groupName: 考勤记录
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取打卡记录列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      config_id:
        name: config_id
        description: 打卡配置ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      user_id:
        name: user_id
        description: 用户ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: '状态(0-正常,1-异常)'
        required: false
        example: 0
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_start:
        name: date_start
        description: 开始日期
        required: false
        example: '2025-08-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_end:
        name: date_end
        description: 结束日期
        required: false
        example: '2025-08-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页记录数
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      config_id: 1
      user_id: 1
      status: 0
      date_start: '2025-08-01'
      date_end: '2025-08-31'
      page: 1
      per_page: 15
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[],"links":{"first":"http://localhost:8005/api/admin/checkin-records?page=1","last":"http://localhost:8005/api/admin/checkin-records?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":null,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"http://localhost:8005/api/admin/checkin-records?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"http://localhost:8005/api/admin/checkin-records","per_page":15,"to":null,"total":0}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/checkin-records
    metadata:
      groupName: 考勤记录
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 打卡操作
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      checkin_config_id:
        name: checkin_config_id
        description: 打卡配置ID
        required: true
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location:
        name: location
        description: 打卡地点
        required: false
        example: 公司大门
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      latitude:
        name: latitude
        description: 打卡位置经度
        required: false
        example: '39.9042'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: 打卡位置纬度
        required: false
        example: '116.4074'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location_range:
        name: location_range
        description: 打卡位置范围
        required: false
        example: 50
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      attachment_id:
        name: attachment_id
        description: 打卡照片附件ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      content:
        name: content
        description: 打卡备注
        required: false
        example: 正常打卡
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      ip_address:
        name: ip_address
        description: IP地址
        required: false
        example: 127.0.0.1
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      checkin_config_id: 2
      location: 公司大门
      latitude: '39.9042'
      longitude: '116.4074'
      location_range: 50
      attachment_id: 1
      content: 正常打卡
      ip_address: 127.0.0.1
    fileParameters: []
    responses:
      -
        status: 201
        content: '{"id":22,"checkin_time":1756102115,"status":1,"location":"公司大门","longitude":"116.4074","latitude":"39.9042","location_range":50,"ip_address":"127.0.0.1","content":"正常打卡","attachment_id":1,"attachment":{"id":1,"file_name":"u=*********,4182719556&fm=253&app=138&f=JPEG.jpg","file_url":"http://localhost:8005/storage/attachments/2025/08/23/wizRN7suzBiqOkstFnCDKNVJnJAomhxK.jpg","file_size":59351,"formatted_file_size":"57.96 KB","mime_type":"image/jpeg","storage_type":"local","created_at":1755955822},"user":{"id":1,"nickname":"admin"}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/checkin-records/{id}'
    metadata:
      groupName: 考勤记录
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取打卡记录详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 记录ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":1,"checkin_time":1755854844,"status":2,"location":"公司大门","longitude":"116.40740000","latitude":"39.90420000","location_range":50,"ip_address":null,"content":"正常打卡","attachment_id":null,"attachment":null,"user":{"id":1,"nickname":"admin"},"config":{"id":2}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
