name: Endpoints
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: 'api/admin/import/{type}/{attachment_id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 统一创建导入任务
      description: |-
        路径参数:
        - type: 导入类型，支持 asset/category/entity/user
        - attachment: 附件ID
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      type:
        name: type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachment_id:
        name: attachment_id
        description: 'The ID of the attachment.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      type: architecto
      attachment_id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 422
        content: '{"message":"不支持的导入类型"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/import/{type}/tasks/{task}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 统一查询导入任务状态
      description: |-
        路径参数:
        - type: 导入类型
        - task: 任务ID
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      type:
        name: type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      task:
        name: task
        description: 'The task.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      type: architecto
      task: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"message":"Server Error"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/import/{type}/tasks'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取指定类型的导入任务列表
      description: |-
        路径参数:
        - type: 导入类型
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      type:
        name: type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      type: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 422
        content: '{"message":"不支持的导入类型"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/export/download/{exportTask_id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 下载导出文件
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      exportTask_id:
        name: exportTask_id
        description: 'The ID of the exportTask.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      exportTask_id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"message":"Server Error"}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
