<?php

use App\Http\Controllers\Admin\AssetController;
use App\Http\Controllers\Admin\AttachmentController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\BrandController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\ConfigController;
use App\Http\Controllers\Admin\DictionaryController;
use App\Http\Controllers\Admin\EntityBrandController;
use App\Http\Controllers\Admin\EntityContactController;
use App\Http\Controllers\Admin\EntityController;
use App\Http\Controllers\Admin\LifecycleController;
use App\Http\Controllers\Admin\LifecycleFollowUpController;
use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\OperationLogController;
use App\Http\Controllers\Admin\QrCodeController;
use App\Http\Controllers\Admin\RegionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\TagController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\UserRoleController;
use App\Http\Controllers\Admin\CheckinConfigController;
use App\Http\Controllers\Admin\CheckinRecordController;
use Illuminate\Support\Facades\Route;

Route::prefix('admin')->group(function () {
    // 登录
    Route::post('/login', [AuthController::class, 'login'])->name('admin.login');

    // 二维码管理
    Route::prefix('qrcode')->group(function () {
        Route::post('generate', [QrCodeController::class, 'generate'])->name('qrcode.generate');
    });

    Route::middleware('auth:sanctum')->group(function () {
        // 退出
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

        // 字典管理
        Route::prefix('dictionary')->group(function () {
            // 字典分类管理
            Route::get('/categories', [DictionaryController::class, 'categoryIndex'])->name('dictionary.categories.index');
            Route::post('/categories', [DictionaryController::class, 'categoryStore'])->name('dictionary.categories.store');
            Route::put('/categories/{category}', [DictionaryController::class, 'categoryUpdate'])->name('dictionary.categories.update');
            Route::delete('/categories/{category}', [DictionaryController::class, 'categoryDestroy'])->name('dictionary.categories.destroy');

            // 字典项管理
            Route::get('/items', [DictionaryController::class, 'itemIndex'])->name('dictionary.items.index');
            Route::post('/items', [DictionaryController::class, 'itemStore'])->name('dictionary.items.store');
            Route::put('/items/{item}', [DictionaryController::class, 'itemUpdate'])->name('dictionary.items.update');
            Route::delete('/items/{item}', [DictionaryController::class, 'itemDestroy'])->name('dictionary.items.destroy');

            // 根据分类编码获取字典项
            Route::get('/code/{categoryCode}', [DictionaryController::class, 'getByCode'])->name('dictionary.code.show');
            // 兼容旧API
            Route::get('/children/{categoryCode}', [DictionaryController::class, 'getChildren'])->name('dictionary.children.show');
        });

        // 分类管理
        Route::prefix('categories')->group(function () {
            Route::get('/', [CategoryController::class, 'index'])->name('categories.index');
            Route::post('/', [CategoryController::class, 'store'])->name('categories.store');
            Route::put('/{category}', [CategoryController::class, 'update'])->name('categories.update');
            Route::delete('/{category}', [CategoryController::class, 'destroy'])->name('categories.destroy');
            Route::get('/children/{parentId}', [CategoryController::class, 'getChildren'])->name('categories.children.show');


        });

        // 品牌管理
        Route::prefix('brands')->group(function () {
            Route::get('/', [BrandController::class, 'index'])->name('brands.index');
        });

        // 用户管理
        Route::apiResource('users', UserController::class);


        // 用户角色管理
        Route::prefix('users/{user}/roles')->group(function () {
            Route::post('/assign', [UserRoleController::class, 'assign'])->name('users.roles.assign');
            Route::put('/sync', [UserRoleController::class, 'sync'])->name('users.roles.sync');
            Route::delete('/remove', [UserRoleController::class, 'remove'])->name('users.roles.remove');
        });

        // 角色管理
        Route::apiResource('roles', RoleController::class);

        // 角色菜单权限管理
        Route::prefix('roles/{role}/menu-permissions')->group(function () {
            Route::post('assign', [RoleController::class, 'assign'])->name('roles.menu-permissions.assign');
        });

        // 统一导入接口
        Route::prefix('import')->group(function () {
            // 创建导入任务：/admin/import/{type}/{attachment}
            Route::post('/{type}/{attachment}', [\App\Http\Controllers\Admin\ImportController::class, 'import'])->name('import.create');
            // 查询导入状态：/admin/import/{type}/tasks/{task}
            Route::get('/{type}/tasks/{task}', [\App\Http\Controllers\Admin\ImportController::class, 'status'])->name('import.status');
            // 获取导入任务列表：/admin/import/{type}/tasks
            Route::get('/{type}/tasks', [\App\Http\Controllers\Admin\ImportController::class, 'list'])->name('import.list');
        });

        // 统一导出接口
        Route::prefix('export')->group(function () {
            // 导出模板：/admin/export/{type}/template
            // Route::get('/{type}/template', [\App\Http\Controllers\Admin\ExportController::class, 'template'])->name('export.template');
            // 下载导出文件：/admin/export/download/{exportTask}
            Route::get('/download/{exportTask}', [\App\Http\Controllers\Admin\ExportController::class, 'download'])->name('export.download');
        });

        // 相关方管理
        Route::prefix('entities')->group(function () {
            Route::get('/', [EntityController::class, 'index'])->name('entities.index');
            Route::post('/', [EntityController::class, 'store'])->name('entities.store');


            Route::get('/{entity}', [EntityController::class, 'show'])->name('entities.show');
            Route::put('/{entity}', [EntityController::class, 'update'])->name('entities.update');
            Route::delete('/{entity}', [EntityController::class, 'destroy'])->name('entities.destroy');
        });

        // 相关方联系人管理
        Route::prefix('entities/{entity}/contacts')->group(function () {
            Route::get('/', [EntityContactController::class, 'index'])->name('entities.contacts.index');
            Route::post('/', [EntityContactController::class, 'store'])->name('entities.contacts.store');
            Route::put('/{contact}', [EntityContactController::class, 'update'])->name('entities.contacts.update');
            Route::delete('/{contact}', [EntityContactController::class, 'destroy'])->name('entities.contacts.destroy');
        });

        // 相关方品牌管理
        Route::prefix('entities/{entity}/brands')->group(function () {
            Route::get('/', [EntityBrandController::class, 'index'])->name('entities.brands.index');
            Route::post('/', [EntityBrandController::class, 'store'])->name('entities.brands.store');
            Route::put('/{brand}', [EntityBrandController::class, 'update'])->name('entities.brands.update');
            Route::delete('/{brand}', [EntityBrandController::class, 'destroy'])->name('entities.brands.destroy');
        });

        // 附件管理
        Route::prefix('attachments')->group(function () {
            Route::get('/', [AttachmentController::class, 'index'])->name('attachments.index');
            Route::post('/upload', [AttachmentController::class, 'upload'])->name('attachments.upload');
            Route::get('/{attachment}', [AttachmentController::class, 'show'])->name('attachments.show');
            Route::get('/{attachment}/download', [AttachmentController::class, 'download'])->name('attachments.download');
            // STS直传相关接口
            Route::post('/sts/credentials', [AttachmentController::class, 'getSTSCredentials'])->name('attachments.sts.credentials');
            Route::post('/sts/confirm', [AttachmentController::class, 'confirmUpload'])->name('attachments.sts.confirm');
            // 业务关联接口
            Route::get('/by-business', [AttachmentController::class, 'getByBusiness'])->name('attachments.by-business');
            // 根据附件关联关系更描述
            Route::put('/update-by-relation/{attachmentRelation}', [AttachmentController::class, 'updateByAttachmentRelation'])->name('attachments.update-by-relation');
        });

        // 生命周期管理
        Route::prefix('lifecycles')->group(function () {
            Route::get('/', [LifecycleController::class, 'index'])->name('lifecycles.index');
            Route::post('/', [LifecycleController::class, 'store'])->name('lifecycles.store');
            Route::get('/{id}', [LifecycleController::class, 'show'])->name('lifecycles.show');
            Route::put('/{id}', [LifecycleController::class, 'update'])->name('lifecycles.update');
            Route::delete('/{id}', [LifecycleController::class, 'destroy'])->name('lifecycles.destroy');
            Route::put('/{id}/accept', [LifecycleController::class, 'accept'])->name('lifecycles.accept');

            // 获取验收人员列表
            Route::get('/entities/{entityId}/acceptance-personnel', [LifecycleController::class, 'getAcceptancePersonnel'])->name('lifecycles.acceptance-personnel');

            // 标签同步
            Route::put('/{id}/tags', [LifecycleController::class, 'syncTags'])->name('lifecycles.tags.sync');

            // 跟进记录管理
            Route::prefix('{lifecycleId}/follow-ups')->group(function () {
                Route::post('/', [LifecycleFollowUpController::class, 'store'])->name('lifecycles.follow-ups.store');
                Route::get('/{id}', [LifecycleFollowUpController::class, 'show'])->name('lifecycles.follow-ups.show');
                Route::put('/{id}', [LifecycleFollowUpController::class, 'update'])->name('lifecycles.follow-ups.update');
                Route::delete('/{id}', [LifecycleFollowUpController::class, 'destroy'])->name('lifecycles.follow-ups.destroy');
            });

            

            // 考勤打卡开关
            Route::put('/{id}/toggle-checkin', [LifecycleController::class, 'toggleCheckin'])->name('lifecycles.checkin.toggle');

        });

        // 地区管理
        Route::prefix('regions')->group(function () {
            Route::get('/tree', [RegionController::class, 'tree'])->name('regions.tree');
            Route::get('/children/{parentId}', [RegionController::class, 'children'])->name('regions.children');
            Route::get('/path/{code}', [RegionController::class, 'path'])->name('regions.path');
            Route::get('/search', [RegionController::class, 'search'])->name('regions.search');
            Route::get('/provinces', [RegionController::class, 'provinces'])->name('regions.provinces');
            Route::get('/cities/{provinceId}', [RegionController::class, 'cities'])->name('regions.cities');
            Route::get('/districts/{cityId}', [RegionController::class, 'districts'])->name('regions.districts');
        });

        // 资产管理
        Route::prefix('assets')->group(function () {
            Route::get('/', [AssetController::class, 'index'])->name('assets.index');
            Route::post('/', [AssetController::class, 'store'])->name('assets.store');
            Route::get('/main-assets', [AssetController::class, 'mainAssets'])->name('assets.main-assets');



            Route::get('/{asset}', [AssetController::class, 'show'])->name('assets.show');
            Route::put('/{asset}', [AssetController::class, 'update'])->name('assets.update');
            Route::post('/batch/copy', [AssetController::class, 'batchCopy'])->name('assets.batch.copy');
            Route::post('/batch/destroy', [AssetController::class, 'batchDestroy'])->name('assets.batch.destroy');
            Route::delete('/{asset}', [AssetController::class, 'destroy'])->name('assets.destroy');
        });

        // 菜单管理
        Route::prefix('menus')->group(function () {
            Route::get('/', [MenuController::class, 'index'])->name('menus.index');
            Route::get('/tree', [MenuController::class, 'tree'])->name('menus.tree');
            Route::post('/', [MenuController::class, 'store'])->name('menus.store');
            Route::put('/{menu}', [MenuController::class, 'update'])->name('menus.update');
            Route::delete('/{menu}', [MenuController::class, 'destroy'])->name('menus.destroy');
        });

        // 操作日志管理
        Route::prefix('operation-logs')->group(function () {
            Route::get('/', [OperationLogController::class, 'index'])->name('operation-logs.index');
            Route::get('/{operationLog}', [OperationLogController::class, 'show'])->name('operation-logs.show');
            Route::get('/stats/operation-types', [OperationLogController::class, 'operationTypeStats'])->name('operation-logs.stats.operation-types');
            Route::get('/stats/menus', [OperationLogController::class, 'menuStats'])->name('operation-logs.stats.menus');
        });

        // 配置管理
        Route::prefix('configs')->group(function () {
            Route::get('/', [ConfigController::class, 'index'])->name('configs.index');
            Route::put('/update', [ConfigController::class, 'update'])->name('configs.update');
            Route::post('/clear-cache', [ConfigController::class, 'clearCache'])->name('configs.clear-cache');
        });

        // 考勤配置
        Route::prefix('checkin-configs')->group(function () {
            Route::get('/', [CheckinConfigController::class, 'index'])->name('checkin-configs.index');
            Route::post('/', [CheckinConfigController::class, 'store'])->name('checkin-configs.store');
            Route::put('/{checkinConfig}', [CheckinConfigController::class, 'update'])->name('checkin-configs.update');
            Route::get('/{checkinConfig}', [CheckinConfigController::class, 'show'])->name('checkin-configs.show');
            // Route::put('/{checkinConfig}/switch', [CheckinConfigController::class, 'switch'])->name('checkin-configs.switch');
        });

        // 考勤记录
        Route::apiResource('checkin-records', CheckinRecordController::class)->only(['index', 'store', 'show']);

        // 标签管理
        Route::prefix('tags')->group(function () {
            Route::get('/', [TagController::class, 'index'])->name('tags.index');
            Route::get('/all', [TagController::class, 'all'])->name('tags.all');
            Route::post('/', [TagController::class, 'store'])->name('tags.store');
            Route::put('/{id}', [TagController::class, 'update'])->name('tags.update');
            Route::delete('/{id}', [TagController::class, 'destroy'])->name('tags.destroy');
        });
    });
});
