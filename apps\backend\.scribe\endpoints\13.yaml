name: 菜单管理
description: |-

  系统菜单管理接口
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/menus
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取菜单列表
      description: 获取当前用户有权限访问的菜单扁平数组，前端自行构建树形结构
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "menuList": [
              {
                "id": 1,
                "parent_id": 0,
                "name": "User",
                "path": "/user",
                "component": "User",
                "title": "用户管理",
                "icon": "user",
                "label": "user",
                "sort": 1,
                "is_hide": false,
                "is_hide_tab": false,
                "link": "https://www.baidu.com",
                "is_iframe": false,
                "keep_alive": true,
                "is_first_level": false,
                "fixed_tab": false,
                "active_path": "/user",
                "is_full_page": false,
                "show_badge": false,
                "show_text_badge": "new",
                "status": true,
                "meta": {
                  "title": "用户管理",
                  "icon": "user",
                  "keepAlive": true,
                  "showBadge": false,
                  "showTextBadge": "new",
                  "isHide": false,
                  "isHideTab": false,
                  "link": "https://www.baidu.com",
                  "isIframe": false,
                  "authList": [
                    {
                      "title": "用户列表",
                      "authMark": "user:list"
                    }
                  ],
                  "isFirstLevel": false,
                  "fixedTab": false,
                  "activePath": "/user",
                  "isFullPage": false
                }
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/menus/tree
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取菜单树
      description: 用于选择父级菜单
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "data": [
              {
                "id": 1,
                "parent_id": 0,
                "name": "User",
                "path": "/user",
                "component": "User",
                "title": "用户管理",
                "icon": "user",
                "label": "user",
                "sort": 1,
                "is_hide": false,
                "is_hide_tab": false,
                "link": "https://www.baidu.com",
                "is_iframe": false,
                "keep_alive": true,
                "is_first_level": false,
                "fixed_tab": false,
                "active_path": "/user",
                "is_full_page": false,
                "show_badge": false,
                "show_text_badge": "new",
                "status": true,
                "meta": {
                  "title": "用户管理",
                  "icon": "user",
                  "keepAlive": true,
                  "showBadge": false,
                  "showTextBadge": "new",
                  "isHide": false,
                  "isHideTab": false,
                  "link": "https://www.baidu.com",
                  "isIframe": false,
                  "authList": [
                    {
                      "title": "用户列表",
                      "authMark": "user:list"
                    }
                  ],
                  "isFirstLevel": false,
                  "fixedTab": false,
                  "activePath": "/user",
                  "isFullPage": false
                },
                "children": [
                  {
                    "id": 2,
                    "parent_id": 1,
                    "name": "UserList",
                    "path": "/user/list",
                    "component": "UserList",
                    "title": "用户列表",
                    "icon": "user",
                    "label": "user:list",
                    "sort": 1,
                    "is_hide": false,
                    "is_hide_tab": false,
                    "link": "https://www.baidu.com",
                    "is_iframe": false,
                    "keep_alive": true,
                    "is_first_level": false,
                    "fixed_tab": false,
                    "active_path": "/user/list",
                    "is_full_page": false,
                    "show_badge": false,
                    "show_text_badge": "new",
                    "status": true,
                    "meta": {
                      "title": "用户列表",
                      "icon": "user",
                      "keepAlive": true,
                      "showBadge": false,
                      "showTextBadge": "new",
                      "isHide": false,
                      "isHideTab": false,
                      "link": "https://www.baidu.com",
                      "isIframe": false,
                      "authList": [
                        {
                          "title": "用户列表",
                          "authMark": "user:list"
                        }
                      ],
                      "isFirstLevel": false,
                      "fixedTab": false,
                      "activePath": "/user/list",
                      "isFullPage": false
                    }
                  }
                ]
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/menus
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 创建菜单
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      parent_id:
        name: parent_id
        description: 父级菜单ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      name:
        name: name
        description: 菜单名称
        required: true
        example: 用户管理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      path:
        name: path
        description: 菜单路径
        required: true
        example: /user
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      component:
        name: component
        description: 组件路径
        required: false
        example: User
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      title:
        name: title
        description: 菜单标题
        required: true
        example: 用户管理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      icon:
        name: icon
        description: 菜单图标
        required: false
        example: user
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      label:
        name: label
        description: 权限标识
        required: false
        example: user
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      sort:
        name: sort
        description: 排序
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_hide:
        name: is_hide
        description: 是否隐藏
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_hide_tab:
        name: is_hide_tab
        description: 是否在标签页隐藏
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      link:
        name: link
        description: 外部链接
        required: false
        example: 'https://www.baidu.com'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_iframe:
        name: is_iframe
        description: 是否为iframe
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keep_alive:
        name: keep_alive
        description: 是否缓存
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_first_level:
        name: is_first_level
        description: 是否为一级菜单
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      fixed_tab:
        name: fixed_tab
        description: 是否固定标签页
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      active_path:
        name: active_path
        description: 激活菜单路径
        required: false
        example: /user
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_full_page:
        name: is_full_page
        description: 是否全屏页面
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      show_badge:
        name: show_badge
        description: 是否显示徽章
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      show_text_badge:
        name: show_text_badge
        description: 文本徽章内容
        required: false
        example: new
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      status:
        name: status
        description: 状态
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      permissions:
        name: permissions
        description: 权限列表
        required: false
        example:
          -
            title: 用户列表
            auth_mark: 'user:list'
            sort: 1
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions[].title':
        name: 'permissions[].title'
        description: 'This field is required when <code>permissions</code> is present. Must not be greater than 50 characters.'
        required: false
        example: m
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].auth_mark':
        name: 'permissions[].auth_mark'
        description: 'Must not be greater than 100 characters.'
        required: false
        example: i
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'permissions[].sort':
        name: 'permissions[].sort'
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions.*.title':
        name: 'permissions.*.title'
        description: 权限名称
        required: true
        example: 用户列表
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.auth_mark':
        name: 'permissions.*.auth_mark'
        description: 权限标识
        required: true
        example: 'user:list'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.sort':
        name: 'permissions.*.sort'
        description: 排序
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      parent_id: 1
      name: 用户管理
      path: /user
      component: User
      title: 用户管理
      icon: user
      label: user
      sort: 1
      is_hide: false
      is_hide_tab: false
      link: 'https://www.baidu.com'
      is_iframe: false
      keep_alive: true
      is_first_level: false
      fixed_tab: false
      active_path: /user
      is_full_page: false
      show_badge: false
      show_text_badge: new
      status: true
      permissions:
        -
          title: 用户列表
          auth_mark: 'user:list'
          sort: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "data": {
              "id": 1,
              "parent_id": 0,
              "name": "User",
              "path": "/user",
              "component": "User",
              "title": "用户管理",
              "icon": "user",
              "label": "user",
              "sort": 1,
              "is_hide": false,
              "is_hide_tab": false,
              "link": "https://www.baidu.com",
              "is_iframe": false,
              "keep_alive": true,
              "is_first_level": false,
              "fixed_tab": false,
              "active_path": "/user",
              "is_full_page": false,
              "show_badge": false,
              "show_text_badge": "new",
              "status": true,
              "meta": {
                "title": "用户管理",
                "icon": "user",
                "keepAlive": true,
                "showBadge": false,
                "showTextBadge": "new",
                "isHide": false,
                "isHideTab": false,
                "link": "https://www.baidu.com",
                "isIframe": false,
                "authList": [
                  {
                    "title": "用户列表",
                    "authMark": "user:list"
                  }
                ],
                "isFirstLevel": false,
                "fixedTab": false,
                "activePath": "/user",
                "isFullPage": false
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/menus/{id}'
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 更新菜单
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the menu.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      parent_id:
        name: parent_id
        description: 父级菜单ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      name:
        name: name
        description: 菜单名称
        required: true
        example: 用户管理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      path:
        name: path
        description: 菜单路径
        required: true
        example: /user
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      component:
        name: component
        description: 组件路径
        required: false
        example: User
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      title:
        name: title
        description: 菜单标题
        required: true
        example: 用户管理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      icon:
        name: icon
        description: 菜单图标
        required: false
        example: user
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      label:
        name: label
        description: 权限标识
        required: false
        example: user
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      sort:
        name: sort
        description: 排序
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_hide:
        name: is_hide
        description: 是否隐藏
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_hide_tab:
        name: is_hide_tab
        description: 是否在标签页隐藏
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      link:
        name: link
        description: 外部链接
        required: false
        example: 'https://www.baidu.com'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_iframe:
        name: is_iframe
        description: 是否为iframe
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keep_alive:
        name: keep_alive
        description: 是否缓存
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_first_level:
        name: is_first_level
        description: 是否为一级菜单
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      fixed_tab:
        name: fixed_tab
        description: 是否固定标签页
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      active_path:
        name: active_path
        description: 激活菜单路径
        required: false
        example: /user
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_full_page:
        name: is_full_page
        description: 是否全屏页面
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      show_badge:
        name: show_badge
        description: 是否显示徽章
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      show_text_badge:
        name: show_text_badge
        description: 文本徽章内容
        required: false
        example: new
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      status:
        name: status
        description: 状态
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      permissions:
        name: permissions
        description: 权限列表
        required: false
        example:
          -
            title: 用户列表
            auth_mark: 'user:list'
            sort: 1
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions[].title':
        name: 'permissions[].title'
        description: 'This field is required when <code>permissions</code> is present. Must not be greater than 50 characters.'
        required: false
        example: m
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions[].auth_mark':
        name: 'permissions[].auth_mark'
        description: 'Must not be greater than 100 characters.'
        required: false
        example: i
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'permissions[].sort':
        name: 'permissions[].sort'
        description: ''
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'permissions.*.title':
        name: 'permissions.*.title'
        description: 权限名称
        required: true
        example: 用户列表
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.auth_mark':
        name: 'permissions.*.auth_mark'
        description: 权限标识
        required: true
        example: 'user:list'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'permissions.*.sort':
        name: 'permissions.*.sort'
        description: 排序
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      parent_id: 1
      name: 用户管理
      path: /user
      component: User
      title: 用户管理
      icon: user
      label: user
      sort: 1
      is_hide: false
      is_hide_tab: false
      link: 'https://www.baidu.com'
      is_iframe: false
      keep_alive: true
      is_first_level: false
      fixed_tab: false
      active_path: /user
      is_full_page: false
      show_badge: false
      show_text_badge: new
      status: true
      permissions:
        -
          title: 用户列表
          auth_mark: 'user:list'
          sort: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "data": {
              "id": 1,
              "parent_id": 0,
              "name": "User",
              "path": "/user",
              "component": "User",
              "title": "用户管理",
              "icon": "user",
              "label": "user",
              "sort": 1,
              "is_hide": false,
              "is_hide_tab": false,
              "link": "https://www.baidu.com",
              "is_iframe": false,
              "keep_alive": true,
              "is_first_level": false,
              "fixed_tab": false,
              "active_path": "/user",
              "is_full_page": false,
              "show_badge": false,
              "show_text_badge": "new",
              "status": true,
              "meta": {
                "title": "用户管理",
                "icon": "user",
                "keepAlive": true,
                "showBadge": false,
                "showTextBadge": "new",
                "isHide": false,
                "isHideTab": false,
                "link": "https://www.baidu.com",
                "isIframe": false,
                "authList": [
                  {
                    "title": "用户列表",
                    "authMark": "user:list"
                  }
                ],
                "isFirstLevel": false,
                "fixedTab": false,
                "activePath": "/user",
                "isFullPage": false
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/menus/{id}'
    metadata:
      groupName: 菜单管理
      groupDescription: |-

        系统菜单管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 删除菜单
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the menu.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      menu:
        name: menu
        description: 菜单ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      menu: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "message": "菜单删除成功"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
