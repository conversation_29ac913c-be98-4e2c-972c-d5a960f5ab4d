name: 授权
description: |-

  管理员认证相关接口
endpoints:
  -
    httpMethods:
      - POST
    uri: api/admin/login
    metadata:
      groupName: 授权
      groupDescription: |-

        管理员认证相关接口
      subgroup: ''
      subgroupDescription: ''
      title: 登录
      description: 管理员登录接口，验证用户名和密码后返回访问令牌
      authenticated: false
      deprecated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      account:
        name: account
        description: 登录账号
        required: true
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      password:
        name: password
        description: 密码
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      account: '***********'
      password: password123
    fileParameters: []
    responses:
      -
        status: 422
        content: '{"message":"账号或密码错误","errors":{"account":["账号或密码错误"]}}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/logout
    metadata:
      groupName: 授权
      groupDescription: |-

        管理员认证相关接口
      subgroup: ''
      subgroupDescription: ''
      title: 退出登录
      description: 撤销当前访问令牌，退出登录状态
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 204
        content: ''
        headers:
          cache-control: 'no-cache, private'
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 1|b9k0973RGFVebzDswNn5Fpk8Ae2xQKpY8BJPiZx64e8ede89'
    controller: null
    method: null
    route: null
    custom: []
